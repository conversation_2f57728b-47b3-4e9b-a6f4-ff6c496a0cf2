<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formula Formatting Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 200px;
        }
        input {
            width: 200px;
            padding: 5px;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .formula {
            font-family: monospace;
            font-size: 16px;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>公式结果千分位格式化测试</h1>
    
    <div class="input-group">
        <label>Total Issued Shares:</label>
        <input type="text" id="totalShares" placeholder="输入总发行股数" value="1000000">
    </div>
    
    <div class="input-group">
        <label>Treasury Shares:</label>
        <input type="text" id="treasuryShares" placeholder="输入库存股数" value="50000">
    </div>
    
    <div class="input-group">
        <label>Percentage (%):</label>
        <input type="text" id="percentage" placeholder="输入百分比" value="10">
    </div>
    
    <button onclick="calculateFormula()">计算公式</button>
    
    <div class="result">
        <h3>公式结果：</h3>
        <div class="formula" id="formulaResult">点击计算按钮查看结果</div>
    </div>

    <script>
        // 格式化数字为千分位显示
        function formatNumberWithCommas(value) {
            if (!value && value !== 0) return '';
            const numStr = value.toString();
            // 移除已有的逗号
            const cleanNum = numStr.replace(/,/g, '');
            // 如果不是有效数字，返回原值
            if (isNaN(cleanNum)) return value;
            // 添加千分位逗号
            return cleanNum.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
        
        // 解析千分位格式的数字为纯数字
        function parseNumberFromCommas(value) {
            if (!value && value !== 0) return '';
            return value.toString().replace(/,/g, '');
        }
        
        function calculateFormulaResult(totalIssuedShares, treasuryShares, percentage) {
            // 格式化数字显示千分位
            const formattedTotalShares = formatNumberWithCommas(totalIssuedShares);
            const formattedTreasuryShares = formatNumberWithCommas(treasuryShares);
            const formattedPercentage = (percentage).toFixed(2);
            
            const resultString = `(${formattedTotalShares} - ${formattedTreasuryShares}) * ${formattedPercentage}%`;
            const result = ((totalIssuedShares - treasuryShares) * percentage / 100).toFixed(2);
            
            return {
                resultString,
                result,
            };
        }
        
        function calculateFormula() {
            const totalSharesInput = document.getElementById('totalShares').value;
            const treasurySharesInput = document.getElementById('treasuryShares').value;
            const percentageInput = document.getElementById('percentage').value;
            
            // 解析输入值
            const totalShares = Number(parseNumberFromCommas(totalSharesInput));
            const treasuryShares = Number(parseNumberFromCommas(treasurySharesInput));
            const percentage = Number(percentageInput);
            
            // 计算公式结果
            const { resultString, result } = calculateFormulaResult(totalShares, treasuryShares, percentage);
            
            // 格式化最终结果也显示千分位
            const formattedFinalResult = formatNumberWithCommas(result);
            const finalFormula = `${resultString} = ${formattedFinalResult}`;
            
            document.getElementById('formulaResult').textContent = finalFormula;
        }
        
        // 为输入框添加实时格式化
        ['totalShares', 'treasuryShares'].forEach(id => {
            document.getElementById(id).addEventListener('input', function(e) {
                const inputValue = e.target.value;
                // 移除非数字字符（保留逗号用于临时处理）
                const cleanValue = inputValue.replace(/[^\d,]/g, '');
                // 移除逗号得到纯数字
                const numericValue = cleanValue.replace(/,/g, '');
                
                // 格式化显示值
                const formattedValue = formatNumberWithCommas(numericValue);
                
                // 设置格式化的值
                if (e.target.value !== formattedValue) {
                    const cursorPosition = e.target.selectionStart;
                    const oldLength = e.target.value.length;
                    e.target.value = formattedValue;
                    
                    // 调整光标位置
                    const newLength = formattedValue.length;
                    const lengthDiff = newLength - oldLength;
                    e.target.setSelectionRange(cursorPosition + lengthDiff, cursorPosition + lengthDiff);
                }
            });
        });
        
        // 初始计算
        calculateFormula();
    </script>
</body>
</html>
