<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Number Formatting Test</title>
</head>
<body>
    <h1>千分位格式化测试</h1>
    
    <div>
        <label>输入数字：</label>
        <input type="text" id="numberInput" placeholder="输入数字">
        <button onclick="formatNumber()">格式化</button>
    </div>
    
    <div>
        <p>格式化结果：<span id="result"></span></p>
        <p>解析结果：<span id="parsed"></span></p>
    </div>

    <script>
        // 格式化数字为千分位显示
        function formatNumberWithCommas(value) {
            if (!value && value !== 0) return '';
            const numStr = value.toString();
            // 移除已有的逗号
            const cleanNum = numStr.replace(/,/g, '');
            // 如果不是有效数字，返回原值
            if (isNaN(cleanNum)) return value;
            // 添加千分位逗号
            return cleanNum.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
        
        // 解析千分位格式的数字为纯数字
        function parseNumberFromCommas(value) {
            if (!value && value !== 0) return '';
            return value.toString().replace(/,/g, '');
        }
        
        function formatNumber() {
            const input = document.getElementById('numberInput');
            const value = input.value;
            
            const formatted = formatNumberWithCommas(value);
            const parsed = parseNumberFromCommas(formatted);
            
            document.getElementById('result').textContent = formatted;
            document.getElementById('parsed').textContent = parsed;
            
            // 更新输入框显示格式化的值
            input.value = formatted;
        }
        
        // 实时格式化
        document.getElementById('numberInput').addEventListener('input', function(e) {
            const inputValue = e.target.value;
            // 移除非数字字符（保留逗号用于临时处理）
            const cleanValue = inputValue.replace(/[^\d,]/g, '');
            // 移除逗号得到纯数字
            const numericValue = cleanValue.replace(/,/g, '');
            
            // 格式化显示值
            const formattedValue = formatNumberWithCommas(numericValue);
            
            // 设置格式化的值
            if (e.target.value !== formattedValue) {
                const cursorPosition = e.target.selectionStart;
                const oldLength = e.target.value.length;
                e.target.value = formattedValue;
                
                // 调整光标位置
                const newLength = formattedValue.length;
                const lengthDiff = newLength - oldLength;
                e.target.setSelectionRange(cursorPosition + lengthDiff, cursorPosition + lengthDiff);
            }
        });
    </script>
</body>
</html>
