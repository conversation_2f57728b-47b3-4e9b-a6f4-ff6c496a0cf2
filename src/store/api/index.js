import * as configuration from './config.api';
import * as project from './project.api';
import * as file from './file.api';
import * as detail from './detail.api';
import * as schema from './schema.api';
import * as remark from './remark.api';
import * as summary from './summary.api';
import * as ruleCheck from './rule-check.api';
import * as ruleAudit from './rule-audit.api';
import * as tag from './tag.api';
import * as user from './user.api';

import * as hkex from './hkex.api';
import * as hkexRule from './hkex.rule.api';
import * as hkexIssuer from './hkex.issuer.api';
import * as hkexQuestion from './hkex.question.api';
import * as hkexSummaryStatistics from './hkex.summary.statistics.api';

import * as ht from './ht.api';
import * as cgs from './cgs.api';
import * as zts from './zts.api';
import * as nafmii from './nafmii.api';
import * as cmfchina from './cmfchina.api';
import * as citicsDCM from './citics-dcm.api';
import * as ccxiContract from './ccxi.contract.api';
import * as laws from './laws.api';

export {
  configuration,
  project,
  file,
  detail,
  schema,
  remark,
  summary,
  ruleCheck,
  ruleAudit,
  tag,
  user,
  hkex,
  hkexRule,
  hkexIssuer,
  hkexQuestion,
  hkexSummaryStatistics,
  ht,
  cgs,
  zts,
  nafmii,
  cmfchina,
  citicsDCM,
  ccxiContract,
  laws,
};
