import { http, uploadTimeout } from './http';

export const fetchAllFiles = (projectId, params) =>
  http.get(`/plugins/fileapi/project/${projectId}/file`, { params });

export const searchFile = (params) =>
  http.get('/plugins/fileapi/file/search', { params });

export const fetchFilesByStatus = (projectId, params) =>
  http.get(`/plugins/fileapi/project/${projectId}/file`, { params });

export const searchKeyword = (fileId, keyword) =>
  http.get(`/plugins/fileapi/file/${fileId}/search?keyword=${keyword}`);

export const fetchRecordHistory = (fileId) =>
  http.get(`/plugins/fileapi/file/${fileId}/history`);

export const fetchFilePageInfo = (fileId) =>
  http.get(`/plugins/fileapi/file/${fileId}/pageinfo`);

export const fetchFileOutline = (fileId) =>
  http.get(`/plugins/fileapi/file/${fileId}/chapter-info`);

export const downloadDocxFile = (fileId) =>
  http.get(`/plugins/fileapi/file/${fileId}/docx`, { responseType: 'blob' });

export const uploadFile = ({ treeId, data, onUploadProgress }) =>
  http.post(`/plugins/fileapi/tree/${treeId}/file`, data, {
    onUploadProgress,
    timeout: uploadTimeout,
  });

export const uploadZipBySSE = ({
  treeId,
  data,
  onUploadProgress,
  onDownloadProgress,
}) =>
  http.post(`/plugins/fileapi/trees/${treeId}/sse`, data, {
    responseType: 'text',
    onUploadProgress,
    onDownloadProgress,
    timeout: uploadTimeout,
  });

/**
 * 重跑文件解析/预测/审核
 * @param {Number} fileId 文件ID
 * @param {Number} task 可选值: pdfinsight/preset/inspect, 分别对应: 解析/预测/审核
 */
export const rerunFile = (fileId, task) =>
  http.get(`/plugins/fileapi/files/${fileId}/run`, {
    params: {
      task,
    },
  });

// 深交所自然语言处理平台 --标注任务列表
export const fetchSZSELabelFiles = (params) =>
  http.get('/szse_file', { params });

export const batchDeleteFiles = (data) =>
  http.post('/plugins/fileapi/files/delete', data);

export const predictFiles = (data, from) => {
  return http.post('/plugins/fileapi/files/execute', data, {
    headers: from ? { 'X-Trigger-Source': from } : undefined,
  });
};

export const batchPredictFiles = (data) => {
  data.task = 'predict';
  return http.post('/plugins/fileapi/files/execute', data);
};

export const batchInspectFiles = (data) => {
  data.task = 'inspect';
  return http.post('/plugins/fileapi/files/execute', data);
};
