import { isEmpty, isEqual } from 'lodash';

// 更新 URL 参数
export function updateSearchParams(ctx, searchParams) {
  const oldSearchParams = formateQueryParams(ctx.$router.currentRoute.query);
  if (isEqual(searchParams, oldSearchParams)) {
    return;
  }
  if (!isEmpty(searchParams)) {
    const replaceParams = {};
    for (const key in searchParams) {
      if (typeof searchParams[key] === 'object') {
        replaceParams[key] = JSON.stringify(searchParams[key]);
      } else {
        replaceParams[key] = searchParams[key];
      }
    }
    ctx.$router.replace({ query: replaceParams });
  } else if (!isEmpty(oldSearchParams)) {
    ctx.$router.replace({ query: {} });
  }
}

// 回填 URL 参数到搜索条件
export function fillSearchParamsFromURL(ctx, searchParams, currentQuery) {
  currentQuery = currentQuery || ctx.$router.currentRoute.query;
  Object.assign(searchParams, formateQueryParams(currentQuery));
}

function formateQueryParams(query) {
  for (const key in query) {
    query[key] = parseValue(query[key]);
  }
  return query;
}

function parseValue(str) {
  try {
    return JSON.parse(str);
  } catch (e) {
    return str;
  }
}
