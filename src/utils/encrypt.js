import Crypt from 'cryptjs';
import platformPerimeter from '@/perimeters/platform.perimeter';

const SECRET_KEY = 'hkx85vMOBSM7M7W';
const mode = platformPerimeter.isHkexEnv() ? 'gcm' : 'cbc';
const crypt = new Crypt(SECRET_KEY, mode);

export function updateB<PERSON>ry<PERSON>ey(key) {
  crypt.decodeBinaryKey(key);
}

export function dataEncrypt(data) {
  const encryptData = crypt.encrypt(
    data,
    !platformPerimeter.isHkexEnv() && SECRET_KEY,
  );
  return encryptData;
}

export function dataDecrypt(data) {
  const decryptData = crypt.decrypt(new Uint8Array(data));
  return decryptData;
}

export function handleBufferToData(data) {
  try {
    const processedData = new TextDecoder().decode(data);
    return processedData && JSON.parse(processedData);
  } catch (error) {
    return data;
  }
}
