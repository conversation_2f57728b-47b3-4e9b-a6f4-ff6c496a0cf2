<template>
  <div>
    <component :is="rulesComponent">
      <template slot="operater">
        <div class="switch-custom-rules-operater">
          <div
            class="tab-button"
            :class="{ active: rulesComponent === 'CustomRules' }"
            @click="changeTab('CustomRules')">
            <theme-icon
              :name="
                rulesComponent === 'CustomRules'
                  ? 'custom-rule'
                  : 'custom-rule-grey'
              "
              icon-class="el-icon-delete"></theme-icon>
            系统规则
          </div>
          <div
            class="tab-button"
            :class="{ active: rulesComponent === 'ModelRules' }"
            @click="changeTab('ModelRules')">
            <theme-icon
              :name="
                rulesComponent === 'ModelRules'
                  ? 'model-rule'
                  : 'model-rule-grey'
              "
              icon-class="el-icon-delete"></theme-icon>
            大模型规则
          </div>
        </div>
      </template>
    </component>
  </div>
</template>
<script>
import CustomRules from './CustomRules.vue';
import ModelRules from '@/custom/general/pages/ModelRules.vue';

export default {
  name: 'switch-custom-rules',
  components: {
    CustomRules,
    ModelRules,
  },

  data() {
    return {
      rulesComponent: 'CustomRules',
    };
  },
  methods: {
    changeTab(component) {
      this.rulesComponent = component;
      this.$router.replace({
        query: {},
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.switch-custom-rules-operater {
  display: flex;
  align-items: center;
  margin-right: 20px;
  .tab-button {
    padding: 10px 20px;
    border: 1px solid #9a9a9a;
    display: flex;
    align-items: center;
    column-gap: 6px;
    cursor: pointer;
    &:first-child {
      border-radius: 6px 0 0 6px;
      border-right: none;
    }
    &:last-child {
      border-radius: 0 6px 6px 0;
    }
    &.active {
      color: #316ec5;
      border: 1px solid #316ec5;
      background-color: rgba(49, 110, 197, 0.16);
    }
  }
}
</style>
