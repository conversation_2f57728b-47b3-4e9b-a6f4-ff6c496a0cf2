import _ from 'lodash';

const i18n = {
  en: {
    menu: {
      项目管理: 'Projects',
      项目列表: 'Project list',
      Schema管理: 'Schema',
      Schema列表: 'Schema list',
      用户管理: 'Users',
      登出: 'Logout',
      表格抽取: 'Form extraction',
      文件类型管理: 'Tags',
      合同管理: 'Contracts',
      工具栏: 'Tools',
      模板管理: 'Template manage',
      数据推送配置: 'Data push config',
      记录管理: 'Record manage',
      参数值映射配置表: 'Params map config',
      规则管理: 'Rules',
      文档智能提取: 'Intelligent document extraction',
      参数提取稽核: 'Parameter extraction audit',
      产品结果: 'Product results',
      准确率统计: 'Accuracy statistics',
      推送记录: 'Push record',
      系统全景: 'System panorama',
    },
    message: {
      // Project
      文档信息抽取: 'Document information extraction system',
      项目: 'Project',
      新建项目: 'New project',
      新增项目: 'New project',
      编辑项目: 'Edit project',
      新增: 'Create',
      未知: 'Unknow',
      总览: 'Overview',
      名称: 'Name',
      类型: 'Type',
      创建人: 'Founder',
      操作: 'Operation',
      确定: 'OK',
      取消: 'Cancel',
      年报: 'Annual report',
      季报: 'Quarterly report',
      月报: 'Monthly report',
      详情: 'Details',
      编辑: 'Edit',
      删除: 'Delete',
      对比: 'Compare',
      标注: 'Tag',
      查看: 'View',
      历史: 'History',
      合规审核: 'Compliance audit',
      文件: 'Files',
      文件名: 'File name',
      文件数: 'File count',
      ID: 'ID',
      文件ID: 'File ID',
      搜索: 'Search',
      清除: 'Clear',
      位置: 'File path',
      文件夹: 'Folder',
      AI预测: 'AI prediction',
      '场景&AI预测': 'Scene & AI prediction',
      预处理: 'Preprocessing',
      标注人数: 'Tag numbers',
      需标人数: 'People required',
      标注状态: 'Tag status',
      标注进度: 'Tag progress',
      标注用户: 'Tag users',
      上传用户: 'Upload user',
      上传时间: 'Upload time',
      修改时间: 'Updated time',
      文件大小: 'File size',
      文件类型: 'File type',
      产品名称: 'Product name',
      基金管理人名称: 'Fund manager name',
      文件来源: 'File source',
      详细来源: 'Detailed source',
      处理方式: 'Processing type',
      操作时间: 'Operation time',
      列表详情: 'File columns',
      比较完成: 'Compare Finished',
      状态: 'Status',
      已标注: 'Tagged',
      我标注过的: 'I tagged',
      冲突数据: 'Conflicts',
      所有文件: 'All files',
      文件回收站: 'File recycle bin',
      标注管理: 'Tag management',
      schema: 'Schema',
      总文档: 'Total files',
      总页数: 'Total pages',
      总题目: 'Total questions',
      标注完成: 'Tag completed',
      已完成: 'Completed',
      标注人员: 'Tag users',
      待做: 'To be done',
      答题中: 'Answering',
      答题完毕: 'Answer completed',
      已反馈: 'Feedback',
      答案不一致: 'Inconsistent',
      答案一致: 'Consistent',
      反馈已确认: 'Feedback confirmed',
      冲突已处理: 'Conflict confirmed',
      文件比对: 'File compare',
      文档比对: 'File compare',
      批量删除: 'Batch delete',
      批量删除文件成功: 'Batch delete files successfully',
      批量预测: 'Batch predict',
      批量预测和审核: 'Batch predict and inspect',
      重新预测: 'Repredict',
      重新审核: 'Reinspect',
      仅重新审核: 'Reinspect',
      批量审核: 'Batch inspect',
      重新预测和审核: 'Repredict and reinspect',
      新建文件夹: 'New folder',
      上传文件: 'Upload files',
      上传zip: 'Upload zip',
      上传压缩包: 'Upload compressed files',
      上传中: 'Uploading',
      上传出错: 'Upload error',
      '正在上传，请勿切换页面': 'Uploading, please do not switch pages',
      上传成功正在解压缩: 'Upload successfully and unzipping',
      将文件拖到此处: 'Drag and drop files here',
      或: 'or',
      点击上传: 'Click to upload',
      请选择文件: 'Please select files',
      请选择文件类型: 'Please select file type',
      '文件类型不支持，请重新选择': 'File type not supported, please reselect',
      请选择操作类型: 'Please select operation type',
      合同类型: 'Contract type',
      提取: 'Extract',
      说明: 'Note',
      目前支持上传: 'supports upload',
      格式的文档: 'type of files',
      发起: 'Initiate',
      已将文件: 'The file has been',
      与: 'and',
      '发送至Calliper进行比对。': 'has been sent to Calliper for comparison.',
      去查看: 'Go to view',
      关闭: 'Close',
      未知状态: 'Unknow status',
      正在标注: 'Tagging',
      '确定要删除选中的文件吗？': 'Are you sure to delete the selected files?',
      '确定要重新预测选中的文件吗？':
        'Are you sure to repredict the selected files?',
      '是否删除该项目?': 'Confirm to delete this project?',
      '“{text}” 日期格式错误': '"{text}" date format error',
      '“{text}” 数字格式错误': '"{text}" digital format error',
      排队中: 'In the line',
      解析中: 'Parsing',
      已取消: 'Canceled',
      解析成功: 'Parsing successful',
      解析失败: 'Parsing failed',
      OCR过期: 'OCR expired',
      解析异常: 'Parsing exception',
      缓存中: 'Caching',
      清稿文件处理中: 'Cleaning file processing',
      不预测: 'No prediction',
      待预测: 'To be predicted',
      预测中: 'Predicting',
      预测失败: 'Predict failed',
      预测成功: 'Predict successful',
      预测完成: 'Predicted',
      模型未启动: 'Model not enabled',
      模型未关联: 'Model not correlated',
      文件名不能为空: 'File name cannot be empty',
      文件夹名称不能为空: 'Folder name cannot be empty',
      '文件名重复，已有': 'Duplicate folder name, already have',
      '文件处于"{status}"状态，标记，是否以只读形式打开？':
        'The file is "{status}", marked, is it opened as read-only?',
      文件夹创建成功: 'The folder was created successfully',
      上传成功: 'Upload successfully',
      上传失败: 'Upload failure',
      错误: 'Error',
      数据异常: 'Data exception',
      数据校验失败: 'Data verification failed',
      请选择Schema: 'Please select schema',
      未找到匹配的Schema: 'No matching schema found',
      项目名称不能为空: 'Project name cannot be empty',
      无: 'None',
      选择: 'Select',
      请选择: 'Please select',
      段落位置: 'Paragraph location',
      具体结果: 'Precise location',
      目录: 'Directory',
      无目录: 'No directory',
      暂无数据: 'No data',
      暂无预测结果: 'No predict result',
      查看更多: 'View more',
      查看更少: 'View less',
      修改: 'Modify',
      用户ID: 'User ID',
      打开PDF: 'Open PDF',
      打开文档: 'Open document',
      提交答案: 'Submit answer',
      提交记录: 'Submission records',
      暂无提交记录: 'No submission records',
      还原: 'Restore',
      '确定要还原到这次提交吗？': 'Confirm to restore to this submission?',
      答案已还原: 'Answer has been restored',
      后台错误: 'Data error.',
      '获取后台数据错误，可手动编辑标注结果':
        'Data error, you can edit tag by manual.',
      多行标注: 'mutlipart line tag',
      '请在弹出的面板上，进行多行标注': 'Please tag on popup panel.',
      格式错误: 'Format error',
      '导出成功！': 'Export successfully.',
      '登录成功！': 'Login successfully.',
      保存草稿: 'save draft',
      请勿提交空答案: 'Please do not submit an empty answer.',
      '保存答案成功！': 'Save answer successfully.',
      '保存草稿成功！': 'Save draft successfully.',
      '标注失败，请重新标注': 'Tag error, Please tag again',
      '模型尚未完成，请勿选择':
        'AI model has not yet been completed. Please do not choose.',
      推荐答案: 'recommended answer',
      预测答案: 'AI Answer',
      生成预测: 'Generate AI answer',
      推荐答案详情: 'Location Suggestion',
      处理冲突: 'Handle conflicts',
      正确答案: 'Correct answer',
      新增答案: 'Add a blank answer',
      设为正确答案: 'Set to correct answer',
      不能设置推荐答案为正确答案:
        'Cannot set recommended answer as correct answer.',
      只能设置用户答案为正确答案:
        'Can only set the user answer as correct answer.',
      提交修改过的答案并设为正确答案:
        'Submit revised answers and set them as correct answer.',
      设置该用户提交的答案为正确答案:
        'Set the answer submitted by this user to the correct answer.',
      答案设置成功: 'The answer is set successfully.',
      答案设置失败: 'The answer is set fail.',
      答案保存成功: 'The answer is saved successfully.',
      '此操作将永久删除该{type}, 是否继续?':
        'This operation will permanently delete the file. Do you want to continue?',
      '此操作将该{type}放入回收站, 是否继续?':
        'This operation will trash the file. Do you want to continue?',
      此答案由AI推荐: 'The answer is recommended by AI.',
      此题部分答案由AI推荐:
        'Part of the answer to this question is recommended by AI.',
      'AI答案预测中，请稍等...': 'AI answer predicting, please wait...',
      '答案预测中、稍后请重新进入页面查看':
        'The answer is being predicted, please re-enter the page to check later',
      解压缩成功: 'extracted successfully',
      在项目中查看: 'show in project',
      '只能上传.zip格式的文件': 'Only upload files in .zip format',
      '文件大小不能超过{fileMaxSize}MB':
        'File size cannot exceed {fileMaxSize} MB.',
      文件搜索: 'Search files',
      输入不能为空: 'Cannot be empty input',
      请输入数字: 'Please enter the number',
      请输入文件名: 'Please enter a file name',
      请输入文件ID: 'Please enter a file ID',
      请输入标注用户: 'Please enter a tag author',
      合规性检查结果: 'compliance check result',
      新版标注: 'New tag',
      预测位置: 'AI Position',
      关键字: 'Keyword',
      分值: 'Score',
      项目修改成功: 'Project modify successfully',
      项目新建成功: 'Project created successfully',
      项目删除成功: 'Project deleted successfully',
      用户修改成功: 'User modify successfully',
      用户新建成功: 'User created successfully',
      用户删除成功: 'User deleted successfully',
      更新成功: 'Update successfully',
      上一篇文档: 'Previous document',
      下一篇文档: 'Next document',
      该文档尚未指定schema: 'This document has not yet specified a schema',
      该文档正在处理中: 'This document is being processed',
      无信息管理权限: 'No information management permission',
      推送到AutoDoc: 'Push to AutoDoc',
      推送到AutoDoc成功: 'Pushed to AutoDoc successfully',
      合并的答案: 'Merged answer',
      导出的答案: 'Exported answer',
      导出标注答案: 'Export standard answer',
      导出预测答案: 'Export predict answer',
      仅展示差异: 'Show diff only',
      下载转义结果: 'Download escape result',
      该公司已退市: 'The company has been delisted',
      项目名称: 'Project name',
      产品类型: 'Product type',
      产品代码: 'Product code',
      请输入项目名称: 'Please enter a project name',
      请选择产品类型: 'Please select a product type',
      请输入产品名称: 'Please enter a product name',
      请输入产品代码: 'Please enter a product code',
      上传: 'Upload',
      相关文件: 'Relevant documents',
      参数集合: 'parameter set',
      版本号: 'Version',
      模板: 'Template',
      推送状态: 'Push status',
      外部参数来源: 'External parameter sources',
      场景: 'Scene',
      推送时间: 'Push time',
      批次号: 'Batch number',
      所属业务组: 'Business Group Belonging to',
      文档ID: 'Document ID',
      产品名: 'Product Name',
      推送方式: 'Push method',
      比对ID: 'Comparison ID',
      推送类型: 'Push type',
      外部参数ID: 'External parameter ID',
      进入比对界面: 'Enter the comparison interface',
      参数预测: 'Parameter prediction',
      文件上传: 'File upload',
      文件编辑: 'File edit',
      请选择模型: 'Please select a model',
      请选择模板: 'Please select a template',
      请输入版本号: 'Please enter the version number',
      请输入正确的版本号: 'Please enter the correct version number',
      请上传文件: 'Please upload the file',
      请输入文件名称: 'Please enter the file name',
      文件名称: 'File name',
      新增模板: 'Create template',
      编辑模板: 'Edit template',
      模板名称: 'Template name',
      业务组: 'Business group',
      文档分类: 'Document classify',
      所选参数: 'Selected parameters',
      请选择文档: 'Please select a document',
      请输入模板名称: 'Please enter a template name',
      请选择业务组: 'Please select a business group',
      请选择文档分类: 'Please select a document classification',
      请选择参数: 'Please select parameters',
      请选择字段: 'Please select a field',
      请选择所属业务组:
        'Please select the business group to which the file belongs',
      请选择推送的模板: 'Please select the template to be pushed',
      重新推送: 'Re push',
      推送详情: 'Push details',
      重新推送成功: 'Successfully re pushed',
      推送内容查看: 'Push content viewing',
      默认模板: 'Default Template',
      请先选择文档分类: 'Please select a document classification first',
      参数选择: 'Parameter selection',
      新增推送: 'Add push',
      编辑推送: 'Edit push',
      模板ID: 'Template id',
      系统名称: 'System name',
      功能名称: 'Feature name',
      邮件地址: 'Email address',
      回调地址: 'Callback name',
      自动推送: 'Auto push',
      参数模板: 'Parameter template',
      请选择参数模板: 'Please select a parameter template',
      请输入邮件地址: 'Please enter a email address',
      请输入回调地址: 'Please enter the callback address',
      请输入系统名称: 'Please enter the system name',
      请输入功能名称: 'Please enter a feature name',
      请输入正确的邮件地址: 'Please enter the correct email address',
      新增映射关系: 'Add mapping relationship',
      编辑映射关系: 'Edit mapping relationship',
      参数分类: 'Param classify',
      参数名称: 'Param name',
      映射名称: 'Mapping Name',
      参数值: 'Param value',
      映射值: 'Mapped value',
      请选择参数分类: 'Please select a parameter classification',
      请输入参数值: 'Please enter parameter values',
      请选择参数名称: 'Please select the parameter name',
      请输入映射名称: 'Please enter the mapping name',
      请输入映射值: 'Please enter a mapping value',
      映射关系检查: 'Mapping relationship check',
      只展示具有映射关系的文本: 'Only display text with mapping relationships',
      参数推送: 'Parameter push',
      统计状态: 'Statistical status',
      加入统计: 'Join statistics',
      取消统计: 'Cancel statistics',
      加入统计成功: 'Joined statistics successfully',
      取消统计成功: 'Cancel statistics successful',
      已复制: 'Copied',
      人工确认状态: 'Confirm status',
      解析开始时间: 'Parsing start time',
      解析完成时间: 'Parsing end time',
      任务类型: 'Task type',

      // Schema
      新增Schema: 'Create schema',
      编辑Schema: 'Edit schema',
      创建时间: 'Creation time',
      字段名称: 'Field name',
      字段别名: 'Field alias',
      字段类型: 'Field type',
      必填: 'Required',
      是否必填: 'Is it required?',
      是否可多选: 'Are multiple choices allowed?',
      顶级Schema: 'Top schema',
      文本: 'Text',
      日期: 'Date',
      数字: 'Number',
      Schema树: 'Schema tree',
      类型管理: 'Type management',
      模型: 'Model',
      模型管理: 'Model management',
      模型版本总数: 'Total Model Versions',
      新建版本: 'Create new version',
      版本名称: 'Version name',
      模型版本名称: 'Model version name',
      复制已有版本: 'Copy an existing version',
      选择已有版本: 'Select an existing version',
      训练文件基数: 'Training file cardinality',
      最新准确率: 'Latest accuracy',
      测试准确率: 'Test accuracy',
      查看准确率: 'View accuracy',
      查看版本差异: 'View version differences',
      模型状态: 'Model state',
      启用模型: 'Enable model',
      停用模型: 'Disable model',
      删除模型: 'Delete model',
      开始训练: 'Start training',
      新建模型版本成功: 'Create model version successfully',
      '确定要删除模型 "{name}" 吗？': 'Confirm to delete model "{name}"？',
      '确定要停用模型 "{name}" 吗？': 'Confirm to disable model "{name}"？',
      模型配置更新成功: 'Model configuration updated successfully',
      启用模型成功: 'Model enabled successfully',
      停用模型成功: 'Model disabled successfully',
      确定启用该模型: 'Make sure to enable the model',
      利用该模型重新预测已有文件: 'Use the model to re-predict existing files',
      '字段均未设置提取模式，不能训练':
        'All fields are not set extraction mode, can not be trained',
      训练模型: 'Train model',
      启用状态: 'Enabled state',
      未启用: 'Not enabled',
      已启用: 'Enabled',
      提取模式查看: 'Extraction mode view',
      提取模式设置: 'Extraction mode settings',
      自定义提取正则: 'Custom extraction regex',
      请确保样本文件已预处理并标注完成:
        'Make sure the sample files are preprocessed and annotated',
      该文件夹下样本: 'Samples in this folder',
      选择样本: 'Choose a sample',
      按原有样本范围: 'According to the original sample range',
      重新选择新样本: 'Reselect new sample',
      '不选择范围，将引用该schema的所有文件标注答案进行训练':
        'Do not select a range, all documents referencing the schema will be marked with answers for training',
      定位模型: 'Location model',
      提取模型: 'Extraction model',
      审核规则配置: 'Audit rule configuration',
      提交复核: 'Submit for review',
      提交: 'Submit',
      复核通过: 'Review passed',
      '复核通过，直接删除': 'Review passed and delete',
      复核不通过: 'Review not passed',
      规则判断: 'Rule judgment',
      数据生产: 'Data production',
      添加描述: 'Add description',
      请输入内容: 'Please enter the content',
      字段名: 'Field name',
      必选: 'Required',
      可多选: 'Multiple',
      必须标注: 'Must be marked',
      可标注多项: 'Can be marked multiple',
      保存: 'Save',
      字段数: 'Number of fields',
      名称不能为空: 'Name cannot be empty.',
      别名不能为空: 'Alias cannot be empty.',
      '名称“{name}”已被占用': `The name "{name}" is already in use.`,
      '别名“{alias}”已被占用': `The alias "{alias}" is already in use.`,
      类型不能为空: 'Type cannot be empty.',
      类型不能和上级节点相同: 'Type cannot be the same as the parent node',
      '确定删除"{name}"吗？': 'Confirm to delete "{name}"？',
      类型名称: 'Type name',
      类型别名: 'Type alias',
      新增组合类型: 'Add new combination type',
      编辑组合类型: 'Edit combination type',
      新增枚举类型: 'Add new enumeration type',
      编辑枚举类型: 'Edit enumeration type',
      '确定要删除吗？': 'Confirm to delete?',
      枚举名称: 'Enumeration name',
      新增枚举值: 'Add new enumeration value',
      枚举值: 'Enumeration value',
      设为默认: 'Set as default',
      组合类型: 'Combination type',
      枚举类型: 'Enumeration type',
      基本类型: 'Basic type',
      Schema: 'Schema',
      Schema名称: 'Schema name',
      Schema别名: 'Schema alias',
      Schema类型: 'Schema type',
      Schema暂无具体字段: 'Schema has no specific fields',
      类型存在循环引用: 'Type has circular structure',
      '“{name}”删除成功': '"{name}" deleted.',
      成功: 'Success',
      迁移成功: 'Transfer successfully',
      查看描述: 'Description',
      子Schema: 'Child schema',
      查看Schema: 'View schema',
      迁移Schema: 'Transfer schema',
      正在迁移Schema: 'Schema is transfering',
      关联的Schema: 'Associated schema',
      待指定: 'To be assigned',
      详细信息: 'Details',
      全部取消: 'Cancel all',
      全部选中: 'Select all',
      返回: 'Go back',
      返回首页: 'Back to home page',
      导入: 'Import',
      导出: 'Export',
      批量导出: 'Batch export',
      下载: 'Download',
      导出标注txt: 'Export label txt',
      导出提取结果: 'Export schema answer csv',
      导出审核结果: 'Export schema inspect csv',
      导出目录结构: 'Export catalog structure',
      未开启: 'Unopened',
      未找到答案: 'No answer',
      未分配: 'Unallocated',
      删除成功: 'Deleted successfully',
      'Schema 修改成功': 'Schema modify successfully',
      'Schema 新增成功': 'Schema created successfully',
      'Schema 删除成功': 'Schema deleted successfully',
      类型修改成功: 'Type modify successfully',
      类型删除成功: 'Type delete successfully',
      枚举值不能为空: 'Enumeration value cannot be empty',
      枚举值不能重复: 'Enumeration value cannot be repeated',
      '确认要删除已标注项目？': 'Confirm to delete the tagged item?',
      '已有名称为"{label}"的类型': 'There is already a type named "{label}"',
      '当前schema为根节点，请勿标注!':
        'The schema is the root node. Please do not tag it.',

      // Hotkey
      快捷键: 'Hotkey',
      更改类型: 'Change type',
      删除标注框: 'Delete label box',
      绑定多个框: 'Bind multiple boxes',
      合并多个框作为答案: 'Combine multiple boxes as answers',
      提交绑定或合并: 'Submit binding or merge',
      '显示/隐藏类型': 'Show/hide type',
      '弹出标注层， 可以选择文本标注':
        'Pop up the label layer, you can select the text label',
      '已有名称为"{value}"的类型': '已有名称为"{value}"的类型',
      支持枚举多选: 'Enumeration multiple selection',
      '映射关系中有缺失，请检查映射关系':
        'Missing in the mapping relationship, please check the mapping relationship',
      参数推送成功: 'Parameter push successful',

      // Tag
      提示: 'Hint',
      警告: 'Warning',
      字段: 'Fields',
      内容: 'Content',
      编辑标注内容: 'Edit callouts',
      标注内容不能为空: "Tag's content cannot be empty",
      '确定要删除当前规则所有标注么？':
        'Make sure delete all tags of the current rule?',
      '当前schema为组合类型，请勿直接标注':
        'The current schema is a combination type. Please do not tag it directly',
      '尚未选择Schema节点，请勿标注':
        'The schema node has not been selected, Please do not mark',
      '该字段不可标注多项，请勿标注':
        'The schema node cannot be multiple selected, Please do not mark',
      请不要在其他用户下进行标注操作:
        'Please do not mark operations under other users',
      '产品类型已关联项目中的模型，不能被修改':
        'The product type is already associated with a model in the project and cannot be modified',
      暂无具有映射关系的拆分文本:
        'There are currently no split texts with mapping relationships',
      推送内容为空: 'The pushed content is empty',

      // User
      用户名: 'Username',
      权限: 'Permissions',
      基础权限: 'Basic permissions',
      其他权限: 'Additional permissions',
      '是否删除该用户?': 'Confirm to delete this user',
      密码: 'Password',
      新密码: 'New password',
      更新密码: 'Update password',
      项目管理: 'Project management',
      Schema管理: 'Schema management',
      文件类型管理: 'Tag management',
      用户管理: 'User management',
      信息提取: 'Information extraction',
      合同管理: 'Contract management',
      规则管理: 'Rule management-custom rule-handling',
      '规则管理-自定义规则-复核': 'Rule management-custom rule-review',
      规则审核: 'Rule management-developed rule',
      审核: 'Review',
      修改用户: 'Edit user',
      新增用户: 'Add user',
      用户名不能为空: 'Username cannot empty',
      请输入用户名: 'Please enter a username',
      请输入密码: 'Please enter the password',
      请再次输入密码: 'Please enter the password again',
      确认密码: 'Confirm Password',
      密码不能为空: 'password cannot empty',
      '密码设置至少6位，由数字、英文字母、字符组合而成，不能包含空格':
        'The password must be set at least 6 characters, consisting of numbers, English letters, and characters',
      两次输入密码不一致: 'The two passwords are inconsistent',
      备注: 'Note',
      请输入备注: 'Please enter the remarks',
      到期时间: 'Expiration date',
      请选择日期: 'Please select a date',
      请选择权限: 'Please select abilities.',
      权限不能为空: ' Permission cannot be empty',
      用户名不能重复: 'Username cannot repeat',
      浏览: 'Broswer',
      登录: 'Login',
      处理中: 'Working',
      表格抽取: 'Table extraction',

      // Tag
      新增文件类型: 'Add file type',
      修改文件类型: 'Edit file type',
      文件类型删除成功: 'Tag deleted successfully',
      文件类型新建成功: 'Tag created successfully',
      文件类型修改成功: 'Tag modify successfully',
      '是否删除该文件类型?': 'Confirm to delete this tag',

      // HTTP Response Error
      '请求超时，请稍后再试': 'Request timeout, please try again later',
      '会话已过期, 请重新登录': 'The session has expired, please login again',
      系统授权已到期: 'The system authorization has expired',
      错误的请求: 'Bad Request',
      禁止: 'Forbidden',
      未找到: 'Not Found',
      不允许的方法: 'Method Not Allowed',
      服务器内部错误: 'Internal Server Error',
      网关错误: 'Bad Gateway',
      服务不可用: 'Service Unavailable',
      网关超时: 'Gateway Timeout',
      网络错误: 'Network Error',
      未知错误: 'Unknown Error',

      页面未找到或无权限浏览: 'Page not found or no permission to browse',

      检测到新版本: 'A new version is detected',
      点击更新: 'Click to update',

      时: 'h',
      分: 'm',
      秒: 's',
    },
  },
  cn: {},
};

Object.assign(i18n.cn, {
  menu: getKeys(i18n.en.menu),
  message: getKeys(i18n.en.message),
});

function getKeys(msg) {
  return _.reduce(
    msg,
    function (a, v, k) {
      a[k] = k;
      return a;
    },
    {},
  );
}

export default i18n;
