<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JURA Health Monitoring Check</title>
  <link rel="icon" href="data:image/png;base64,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">
  <style>
    html,
    body {
      margin: 0;
    }

    @keyframes rotation {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .container {
      overflow: auto;
    }

    .container .loading {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1;
      background-color: rgba(255, 255, 255, 0.9);
    }

    .container .loading .icon {
      display: inline-block;
      width: 48px;
      height: 48px;
      border: 5px solid #369aa2;
      border-radius: 50%;
      border-bottom-color: transparent;
      box-sizing: border-box;
      animation: rotation 1s linear infinite;
    }

    .container .header {
      position: sticky;
      top: 0;
      display: flex;
      align-items: center;
      padding: 10px 30px;
      color: #3e3f42;
      font-weight: 500;
      background: #fafafa;
      box-shadow: 0px 1px 0px 0px #c7c7c7;
      z-index: 1;
    }

    .container .header svg {
      margin-right: 10px;
    }

    .container .banner svg {
      width: 100%;
      height: 100%;
      vertical-align: bottom;
    }

    .container .dashboard .content {
      padding: 30px;
      color: #6a6a6a;
    }

    .container .dashboard .summary {
      position: relative;
      display: flex;
      align-items: center;
      width: 70%;
      height: 40px;
      margin: -20px auto 0;
      padding: 0 15px;
      border-radius: 3px;
      font-size: 16px;
      color: #fff;
    }

    .container .dashboard .summary .status-icon {
      position: relative;
      width: 12px;
      height: 12px;
      margin-right: 10px;
      border: 2px solid #fff;
    }

    .container .dashboard .summary.ok {
      background-color: #34aa44;
    }

    .container .dashboard .summary.ok .status-icon::before {
      content: "";
      display: inline-block;
      width: 6px;
      height: 3px;
      margin: 0 0 9px 2px;
      border: 1px solid #fff;
      border-width: 0 0 2px 2px;
      transform: rotate(-45deg);
    }

    .container .dashboard .summary.error {
      background-color: #f6ab2f;
    }

    .container .dashboard .summary.error .status-icon::before,
    .container .dashboard .summary.error .status-icon::after {
      position: absolute;
      top: 2px;
      left: 5px;
      width: 2px;
      height: 8px;
      content: "";
      background-color: #fff;
    }

    .container .dashboard .summary.error .status-icon::before {
      transform: rotate(45deg);
    }

    .container .dashboard .summary.error .status-icon::after {
      transform: rotate(-45deg);
    }

    .container .dashboard .content .network {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
    }

    .container .dashboard .content .network .title {
      display: flex;
      flex-flow: column;
      align-items: center;
      margin-right: 30px;
      font-size: 12px;
    }

    .container .dashboard .content .network .info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 300px;
      padding: 10px;
      border-radius: 8px;
      border: 1px solid #c7c7c7;
      background-color: #fafafa;
      font-size: 14px;
      text-align: center;
    }

    .container .dashboard .content table {
      width: 100%;
    }

    .container .dashboard .content table thead {
      background-color: #EFEFEF;
    }

    .container .dashboard .content table thead th {
      padding: 8px;
      font-size: 13px;
    }

    .container .dashboard table tbody tr td {
      padding: 20px;
      border-bottom: 1px solid #EFEFEF;
      font-size: 13px;
    }

    .container .dashboard table td:first-child {
      width: 80px;
      padding-left: 0;
    }

    .container .dashboard table td:not(:first-child) {
      text-align: center;
    }

    .container .dashboard table tbody tr td:nth-child(2),
    .container .dashboard table tbody tr td:nth-child(3),
    .container .dashboard table tbody tr td:nth-child(4),
    .container .dashboard table tbody tr td:nth-child(5),
    .container .dashboard table tbody tr td:nth-child(6) {
      width: 30px;
    }

    .container .dashboard table tr td .cell {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }

    .container .dashboard table tr td .cell .name {
      display: flex;
      flex-flow: column;
      align-items: center;
      margin-right: 15px;
    }

    .container .dashboard table tr td .cell .name .sys {
      margin: 3px 0;
      font-size: 13px;
    }

    .container .dashboard table tr td .cell .name .comment-container {
      display: flex;
      max-width: 80px;
      color: #a5a5a5;
    }

    .container .dashboard table tr td .cell .name .comment-container .comment {
      display: inline-block;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: center;
    }

    .container .dashboard table tr td .green {
      color: #34aa44;
    }

    .container .dashboard table tr td .yellow {
      color: #f6ab2f;
    }
    
    .container .dashboard .content .icon-ok {
      display: inline-block;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background-color: #34aa44;
      transform: scale(0.8);
    }

    .container .dashboard .content .icon-ok::before {
      content: "";
      display: inline-block;
      width: 6px;
      height: 3px;
      margin: 6px 0 7px 0;
      border: 1px solid #fff;
      border-width: 0 0 1px 1px;
      transform: rotate(-45deg);
    }

    .container .dashboard .content .icon-error {
      position: relative;
      display: inline-block;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background-color: #f6ab2f;
      transform: scale(0.8);
    }

    .container .dashboard .content .icon-error::before,
    .container .dashboard .content .icon-error::after {
      position: absolute;
      top: 5px;
      left: 8px;
      width: 2px;
      height: 8px;
      content: "";
      background-color: #fff;
    }

    .container .dashboard .content .icon-error::before {
      transform: rotate(45deg);
    }

    .container .dashboard .content .icon-error::after {
      transform: rotate(-45deg);
    }

    .container .dashboard .content ul {
      max-width: 200px;
      margin: 0 auto;
      padding: 0 10px;
      border-radius: 8px;
      border: 1px solid #c7c7c7;
      background-color: #fafafa;
    }

    .container .dashboard .content ul li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 0;
      list-style: none;
    }

    .container .dashboard .content ul li:not(:last-child) {
      border-bottom: 1px solid #e5e5e5;
    }

    .container .dashboard .content .info {
      display: flex;
      justify-content: space-between;
      width: 100%;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_350_37)">
          <path
            d="M3 5C3 4.73478 3.10536 4.48043 3.29289 4.29289C3.48043 4.10536 3.73478 4 4 4H20C20.2652 4 20.5196 4.10536 20.7071 4.29289C20.8946 4.48043 21 4.73478 21 5V15C21 15.2652 20.8946 15.5196 20.7071 15.7071C20.5196 15.8946 20.2652 16 20 16H4C3.73478 16 3.48043 15.8946 3.29289 15.7071C3.10536 15.5196 3 15.2652 3 15V5Z"
            stroke="#359AA2" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M7 20H17" stroke="#359AA2" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M9 16V20" stroke="#359AA2" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M15 16V20" stroke="#359AA2" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M7 10H9L11 13L13 7L14 10H17" stroke="#359AA2" stroke-width="1.5" stroke-linecap="round"
            stroke-linejoin="round" />
        </g>
        <defs>
          <clipPath id="clip0_350_37">
            <rect width="24" height="24" fill="white" />
          </clipPath>
        </defs>
      </svg>
      JURA Health Monitoring Check
    </div>
    <div class="banner">
      <svg width="1920" height="200" viewBox="0 0 1920 200" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="1920" height="200" fill="#EDEDED" />
        <mask id="mask0_350_144" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1920"
          height="200">
          <rect width="1920" height="200" fill="#E5E5E5" />
        </mask>
        <g mask="url(#mask0_350_144)">
          <g opacity="0.44">
            <path opacity="0.44" d="M78.03 215.37L0 123.06L635.34 -413.98L713.37 -321.66L78.03 215.37Z"
              fill="url(#paint0_radial_350_144)" />
            <path opacity="0.44" d="M625.31 185.86L561.16 109.97L1073.41 -323.02L1137.56 -247.14L625.31 185.86Z"
              fill="url(#paint1_radial_350_144)" />
            <path opacity="0.44" d="M344.28 339.56L294.81 281.03L588.75 32.5801L638.21 91.1L344.28 339.56Z"
              fill="url(#paint2_radial_350_144)" />
            <path opacity="0.44" d="M452.38 124.94L396.26 58.5501L800.47 -283.13L856.59 -216.74L452.38 124.94Z"
              fill="url(#paint3_radial_350_144)" />
            <path opacity="0.44" d="M106.48 357.97L33.27 271.35L404.42 -42.3699L477.63 44.2401L106.48 357.97Z"
              fill="url(#paint4_radial_350_144)" />
            <path opacity="0.44" d="M590.37 345.98L548.33 296.24L890.66 6.87012L932.71 56.6101L590.37 345.98Z"
              fill="url(#paint5_radial_350_144)" />
          </g>
          <g opacity="0.11">
            <path opacity="0.11"
              d="M441.82 83.51C480.751 83.51 512.31 51.9506 512.31 13.0201C512.31 -25.9105 480.751 -57.47 441.82 -57.47C402.89 -57.47 371.33 -25.9105 371.33 13.0201C371.33 51.9506 402.89 83.51 441.82 83.51Z"
              fill="url(#paint6_linear_350_144)" />
            <path opacity="0.11"
              d="M519.94 376.82C597.674 376.82 660.69 313.804 660.69 236.07C660.69 158.336 597.674 95.3201 519.94 95.3201C442.206 95.3201 379.19 158.336 379.19 236.07C379.19 313.804 442.206 376.82 519.94 376.82Z"
              fill="url(#paint7_linear_350_144)" />
            <path opacity="0.11"
              d="M893.03 241.99C986.167 241.99 1061.67 166.487 1061.67 73.3499C1061.67 -19.7874 986.167 -95.29 893.03 -95.29C799.893 -95.29 724.39 -19.7874 724.39 73.3499C724.39 166.487 799.893 241.99 893.03 241.99Z"
              fill="url(#paint8_linear_350_144)" />
            <path opacity="0.11"
              d="M149.16 41.9399C201.235 41.9399 243.45 -0.275208 243.45 -52.3501C243.45 -104.425 201.235 -146.64 149.16 -146.64C97.0852 -146.64 54.8701 -104.425 54.8701 -52.3501C54.8701 -0.275208 97.0852 41.9399 149.16 41.9399Z"
              fill="url(#paint9_linear_350_144)" />
          </g>
          <g opacity="0.72">
            <g opacity="0.72">
              <path opacity="0.72"
                d="M738.71 3.83014L485.23 229.87L481.68 225.89L735.17 -0.149902L738.71 3.83014ZM744.23 10.0101L490.74 236.06L494.29 240.04L747.78 14.0001L744.23 10.0101ZM753.29 20.1801L499.81 246.22L503.36 250.2L756.85 24.1601L753.29 20.1801ZM762.36 30.3401L508.87 256.39L512.42 260.37L765.91 34.3301L762.36 30.3401ZM771.42 40.5101L517.94 266.55L521.49 270.53L774.98 44.4901L771.42 40.5101ZM780.49 50.6701L527 276.72L530.55 280.7L784.04 54.6601L780.49 50.6701ZM789.55 60.8401L536.07 286.88L539.62 290.86L793.1 64.8201L789.55 60.8401ZM798.62 71.0101L545.13 297.05L548.68 301.03L802.17 74.9901L798.62 71.0101ZM807.68 81.1701L554.2 307.22L557.75 311.2L811.24 85.1601L807.68 81.1701ZM816.75 91.3401L563.26 317.38L566.81 321.36L820.3 95.3201L816.75 91.3401ZM825.81 101.5L572.33 327.55L575.88 331.53L829.37 105.49L825.81 101.5ZM834.88 111.67L581.39 337.71L584.94 341.69L838.43 115.65L834.88 111.67ZM843.94 121.83L590.46 347.88L594.01 351.86L847.5 125.82L843.94 121.83ZM853.01 132L599.52 358.04L603.07 362.02L856.56 135.98L853.01 132ZM862.08 142.17L608.59 368.21L612.14 372.19L865.63 146.15L862.08 142.17ZM871.14 152.33L617.65 378.38L621.2 382.36L874.69 156.32L871.14 152.33ZM880.21 162.5L626.72 388.54L630.27 392.52L883.76 166.48L880.21 162.5ZM889.27 172.66L635.78 398.71L639.33 402.69L892.82 176.65L889.27 172.66ZM898.34 182.83L644.85 408.87L648.4 412.85L901.89 186.81L898.34 182.83ZM907.4 192.99L653.91 419.04L657.46 423.02L910.95 196.98L907.4 192.99ZM916.47 203.16L662.98 429.2L666.53 433.18L920.02 207.14L916.47 203.16ZM925.53 213.33L672.04 439.37L675.59 443.35L929.08 217.31L925.53 213.33ZM934.6 223.49L681.11 449.54L684.66 453.52L938.15 227.48L934.6 223.49Z"
                fill="white" />
            </g>
            <g opacity="0.72">
              <path opacity="0.72"
                d="M279.4 -34.7899L174.28 58.9501L172.81 57.3L277.93 -36.4399L279.4 -34.7899ZM281.69 -32.2199L176.57 61.5201L178.04 63.17L283.16 -30.5699L281.69 -32.2199ZM285.45 -28.0099L180.33 65.73L181.8 67.3801L286.92 -26.3599L285.45 -28.0099ZM289.21 -23.7899L184.09 69.9501L185.56 71.6L290.68 -22.14L289.21 -23.7899ZM292.97 -19.5699L187.85 74.17L189.32 75.8201L294.44 -17.9199L292.97 -19.5699ZM296.73 -15.3599L191.6 78.3801L193.07 80.0301L298.19 -13.71L296.73 -15.3599ZM300.49 -11.14L195.36 82.6L196.83 84.2501L301.95 -9.48993L300.49 -11.14ZM304.24 -6.92993L199.12 86.8101L200.59 88.4601L305.71 -5.27997L304.24 -6.92993ZM308 -2.70996L202.88 91.0301L204.35 92.6801L309.47 -1.05994L308 -2.70996ZM311.76 1.50006L206.64 95.2401L208.11 96.8901L313.23 3.15009L311.76 1.50006ZM315.52 5.72003L210.4 99.4601L211.87 101.11L317 7.37006L315.52 5.72003ZM319.28 9.94006L214.16 103.68L215.63 105.33L320.75 11.5901L319.28 9.94006ZM323.04 14.1501L217.92 107.89L219.39 109.54L324.51 15.8L323.04 14.1501ZM326.8 18.3701L221.68 112.11L223.15 113.76L328.27 20.0201L326.8 18.3701ZM330.56 22.5801L225.44 116.32L226.91 117.97L332.03 24.23L330.56 22.5801ZM334.32 26.8L229.2 120.54L230.67 122.19L335.79 28.4501L334.32 26.8ZM338.08 31.0201L232.96 124.76L234.43 126.41L339.55 32.67L338.08 31.0201ZM341.84 35.23L236.72 128.97L238.19 130.62L343.31 36.8801L341.84 35.23ZM345.6 39.4501L240.48 133.19L241.95 134.84L347.07 41.1L345.6 39.4501ZM349.36 43.66L244.24 137.4L245.71 139.05L350.83 45.3101L349.36 43.66ZM353.12 47.8801L248 141.62L249.47 143.27L354.59 49.5301L353.12 47.8801ZM356.88 52.0901L251.76 145.83L253.23 147.48L358.35 53.7401L356.88 52.0901ZM360.64 56.3101L255.52 150.05L256.99 151.7L362.11 57.9601L360.64 56.3101Z"
                fill="white" />
            </g>
            <g opacity="0.72">
              <path opacity="0.72"
                d="M473.86 -14.55L409.42 42.9199L408.52 41.9099L472.96 -15.5601L473.86 -14.55ZM475.26 -12.98L410.82 44.4899L411.72 45.4999L476.16 -11.97L475.26 -12.98ZM477.56 -10.4001L413.12 47.0699L414.02 48.08L478.46 -9.39008L477.56 -10.4001ZM479.87 -7.81006L415.43 49.6599L416.33 50.6699L480.77 -6.80005L479.87 -7.81006ZM482.17 -5.23004L417.73 52.2399L418.63 53.2499L483.07 -4.22003L482.17 -5.23004ZM484.48 -2.64008L420.04 54.83L420.94 55.84L485.38 -1.63007L484.48 -2.64008ZM486.78 -0.0600586L422.34 57.4099L423.24 58.4199L487.68 0.949951L486.78 -0.0600586ZM489.09 2.52997L424.65 59.9999L425.55 61.0099L489.99 3.53992L489.09 2.52997ZM491.39 5.10992L426.95 62.58L427.85 63.59L492.29 6.11993L491.39 5.10992ZM493.7 7.69995L429.26 65.1699L430.16 66.1799L494.6 8.70996L493.7 7.69995ZM496 10.28L431.56 67.7499L432.46 68.7599L496.9 11.2899L496 10.28ZM498.31 12.8599L433.87 70.33L434.77 71.34L499.21 13.8699L498.31 12.8599ZM500.61 15.45L436.17 72.9199L437.07 73.9299L501.51 16.46L500.61 15.45ZM502.92 18.03L438.48 75.4999L439.38 76.5099L503.82 19.0399L502.92 18.03ZM505.22 20.6199L440.78 78.09L441.68 79.0999L506.12 21.6299L505.22 20.6199ZM507.52 23.2L443.08 80.6699L443.98 81.6799L508.42 24.21L507.52 23.2ZM509.83 25.7899L445.39 83.2599L446.29 84.27L510.73 26.7999L509.83 25.7899ZM512.13 28.3699L447.69 85.84L448.59 86.8499L513.03 29.3799L512.13 28.3699ZM514.44 30.96L450 88.4299L450.9 89.4399L515.34 31.9699L514.44 30.96ZM516.74 33.5399L452.3 91.0099L453.2 92.02L517.64 34.5499L516.74 33.5399ZM519.05 36.1199L454.61 93.59L455.51 94.5999L519.95 37.1299L519.05 36.1199ZM521.35 38.71L456.91 96.1799L457.81 97.1899L522.25 39.7199L521.35 38.71ZM523.66 41.2899L459.22 98.7599L460.12 99.77L524.56 42.2999L523.66 41.2899Z"
                fill="white" />
            </g>
            <g opacity="0.72">
              <path opacity="0.72"
                d="M351.72 180.64L287.28 238.11L286.38 237.1L350.82 179.63L351.72 180.64ZM353.13 182.21L288.69 239.68L289.59 240.69L354.03 183.22L353.13 182.21ZM355.43 184.79L290.99 242.26L291.89 243.27L356.33 185.8L355.43 184.79ZM357.74 187.38L293.3 244.85L294.2 245.86L358.64 188.39L357.74 187.38ZM360.04 189.96L295.6 247.43L296.5 248.44L360.94 190.97L360.04 189.96ZM362.35 192.55L297.91 250.02L298.81 251.03L363.25 193.56L362.35 192.55ZM364.65 195.13L300.21 252.6L301.11 253.61L365.55 196.14L364.65 195.13ZM366.95 197.72L302.51 255.19L303.41 256.2L367.85 198.73L366.95 197.72ZM369.26 200.3L304.82 257.77L305.72 258.78L370.16 201.31L369.26 200.3ZM371.56 202.88L307.12 260.35L308.02 261.36L372.46 203.89L371.56 202.88ZM373.87 205.47L309.43 262.94L310.33 263.95L374.77 206.48L373.87 205.47ZM376.17 208.05L311.73 265.52L312.63 266.53L377.07 209.06L376.17 208.05ZM378.48 210.64L314.04 268.11L314.94 269.12L379.38 211.65L378.48 210.64ZM380.78 213.22L316.34 270.69L317.24 271.7L381.68 214.23L380.78 213.22ZM383.09 215.81L318.65 273.28L319.55 274.29L383.99 216.82L383.09 215.81ZM385.39 218.39L320.95 275.86L321.85 276.87L386.29 219.4L385.39 218.39ZM387.7 220.98L323.26 278.45L324.16 279.46L388.6 221.99L387.7 220.98ZM390 223.56L325.56 281.03L326.46 282.04L390.9 224.57L390 223.56ZM392.31 226.14L327.87 283.61L328.77 284.62L393.21 227.15L392.31 226.14ZM394.61 228.73L330.17 286.2L331.07 287.21L395.51 229.74L394.61 228.73ZM396.91 231.31L332.47 288.78L333.37 289.79L397.81 232.32L396.91 231.31ZM399.22 233.9L334.78 291.37L335.68 292.38L400.12 234.91L399.22 233.9ZM401.52 236.48L337.08 293.95L337.98 294.96L402.42 237.49L401.52 236.48Z"
                fill="white" />
            </g>
            <g opacity="0.72">
              <path opacity="0.72"
                d="M913.73 -28L816.56 58.65L815.2 57.12L912.37 -29.53L913.73 -28ZM915.84 -25.63L818.67 61.02L820.03 62.55L917.2 -24.1L915.84 -25.63ZM919.32 -21.73L822.15 64.92L823.51 66.45L920.68 -20.2L919.32 -21.73ZM922.79 -17.83L825.62 68.82L826.98 70.3499L924.15 -16.3L922.79 -17.83ZM926.27 -13.94L829.1 72.71L830.46 74.24L927.63 -12.41L926.27 -13.94ZM929.74 -10.04L832.57 76.61L833.93 78.14L931.1 -8.51004L929.74 -10.04ZM933.22 -6.14005L836.05 80.51L837.41 82.0399L934.58 -4.61002L933.22 -6.14005ZM936.69 -2.24002L839.52 84.4099L840.88 85.94L938.05 -0.710052L936.69 -2.24002ZM940.17 1.64999L843 88.3L844.36 89.83L941.53 3.17996L940.17 1.64999ZM943.64 5.54996L846.47 92.2L847.83 93.7299L945 7.07999L943.64 5.54996ZM947.12 9.44998L849.95 96.0999L851.31 97.63L948.48 10.9799L947.12 9.44998ZM950.59 13.34L853.42 100L854.78 101.53L951.95 14.88L950.59 13.34ZM954.07 17.24L856.9 103.89L858.26 105.42L955.43 18.77L954.07 17.24ZM957.54 21.14L860.37 107.79L861.73 109.32L958.9 22.67L957.54 21.14ZM961.02 25.03L863.85 111.68L865.21 113.21L962.38 26.56L961.02 25.03ZM964.49 28.93L867.32 115.58L868.68 117.11L965.85 30.46L964.49 28.93ZM967.97 32.83L870.8 119.48L872.16 121.01L969.33 34.36L967.97 32.83ZM971.44 36.7299L874.27 123.38L875.63 124.91L972.8 38.26L971.44 36.7299ZM974.92 40.62L877.75 127.27L879.11 128.8L976.28 42.15L974.92 40.62ZM978.39 44.52L881.22 131.17L882.58 132.7L979.75 46.05L978.39 44.52ZM981.87 48.42L884.7 135.07L886.06 136.6L983.23 49.95L981.87 48.42ZM985.34 52.31L888.17 138.96L889.53 140.49L986.7 53.84L985.34 52.31ZM988.82 56.21L891.65 142.86L893.01 144.39L990.18 57.74L988.82 56.21Z"
                fill="white" />
            </g>
          </g>
          <g opacity="0.44">
            <path opacity="0.44" d="M860.03 215.37L782 123.06L1417.34 -413.98L1495.37 -321.66L860.03 215.37Z"
              fill="url(#paint10_radial_350_144)" />
            <path opacity="0.44" d="M1407.31 185.86L1343.16 109.97L1855.41 -323.02L1919.56 -247.14L1407.31 185.86Z"
              fill="url(#paint11_radial_350_144)" />
            <path opacity="0.44" d="M1126.28 339.56L1076.81 281.03L1370.75 32.5801L1420.21 91.1L1126.28 339.56Z"
              fill="url(#paint12_radial_350_144)" />
            <path opacity="0.44" d="M1234.38 124.94L1178.26 58.5501L1582.47 -283.13L1638.59 -216.74L1234.38 124.94Z"
              fill="url(#paint13_radial_350_144)" />
            <path opacity="0.44" d="M888.48 357.97L815.27 271.35L1186.42 -42.3699L1259.63 44.2401L888.48 357.97Z"
              fill="url(#paint14_radial_350_144)" />
            <path opacity="0.44" d="M1372.37 345.98L1330.33 296.24L1672.66 6.87012L1714.71 56.6101L1372.37 345.98Z"
              fill="url(#paint15_radial_350_144)" />
          </g>
          <g opacity="0.11">
            <path opacity="0.11"
              d="M1223.82 83.51C1262.75 83.51 1294.31 51.9506 1294.31 13.0201C1294.31 -25.9105 1262.75 -57.47 1223.82 -57.47C1184.89 -57.47 1153.33 -25.9105 1153.33 13.0201C1153.33 51.9506 1184.89 83.51 1223.82 83.51Z"
              fill="url(#paint16_linear_350_144)" />
            <path opacity="0.11"
              d="M1301.94 376.82C1379.67 376.82 1442.69 313.804 1442.69 236.07C1442.69 158.336 1379.67 95.3201 1301.94 95.3201C1224.21 95.3201 1161.19 158.336 1161.19 236.07C1161.19 313.804 1224.21 376.82 1301.94 376.82Z"
              fill="url(#paint17_linear_350_144)" />
            <path opacity="0.11"
              d="M1675.03 241.99C1768.17 241.99 1843.67 166.487 1843.67 73.3499C1843.67 -19.7874 1768.17 -95.29 1675.03 -95.29C1581.89 -95.29 1506.39 -19.7874 1506.39 73.3499C1506.39 166.487 1581.89 241.99 1675.03 241.99Z"
              fill="url(#paint18_linear_350_144)" />
            <path opacity="0.11"
              d="M931.16 41.9399C983.235 41.9399 1025.45 -0.275208 1025.45 -52.3501C1025.45 -104.425 983.235 -146.64 931.16 -146.64C879.085 -146.64 836.87 -104.425 836.87 -52.3501C836.87 -0.275208 879.085 41.9399 931.16 41.9399Z"
              fill="url(#paint19_linear_350_144)" />
          </g>
          <g opacity="0.72">
            <g opacity="0.72">
              <path opacity="0.72"
                d="M1520.71 3.83014L1267.23 229.87L1263.68 225.89L1517.17 -0.149902L1520.71 3.83014ZM1526.23 10.0101L1272.74 236.06L1276.29 240.04L1529.78 14.0001L1526.23 10.0101ZM1535.29 20.1801L1281.81 246.22L1285.36 250.2L1538.85 24.1601L1535.29 20.1801ZM1544.36 30.3401L1290.87 256.39L1294.42 260.37L1547.91 34.3301L1544.36 30.3401ZM1553.42 40.5101L1299.94 266.55L1303.49 270.53L1556.98 44.4901L1553.42 40.5101ZM1562.49 50.6701L1309 276.72L1312.55 280.7L1566.04 54.6601L1562.49 50.6701ZM1571.55 60.8401L1318.07 286.88L1321.62 290.86L1575.1 64.8201L1571.55 60.8401ZM1580.62 71.0101L1327.13 297.05L1330.68 301.03L1584.17 74.9901L1580.62 71.0101ZM1589.68 81.1701L1336.2 307.22L1339.75 311.2L1593.24 85.1601L1589.68 81.1701ZM1598.75 91.3401L1345.26 317.38L1348.81 321.36L1602.3 95.3201L1598.75 91.3401ZM1607.81 101.5L1354.33 327.55L1357.88 331.53L1611.37 105.49L1607.81 101.5ZM1616.88 111.67L1363.39 337.71L1366.94 341.69L1620.43 115.65L1616.88 111.67ZM1625.94 121.83L1372.46 347.88L1376.01 351.86L1629.5 125.82L1625.94 121.83ZM1635.01 132L1381.52 358.04L1385.07 362.02L1638.56 135.98L1635.01 132ZM1644.08 142.17L1390.59 368.21L1394.14 372.19L1647.63 146.15L1644.08 142.17ZM1653.14 152.33L1399.65 378.38L1403.2 382.36L1656.69 156.32L1653.14 152.33ZM1662.21 162.5L1408.72 388.54L1412.27 392.52L1665.76 166.48L1662.21 162.5ZM1671.27 172.66L1417.78 398.71L1421.33 402.69L1674.82 176.65L1671.27 172.66ZM1680.34 182.83L1426.85 408.87L1430.4 412.85L1683.89 186.81L1680.34 182.83ZM1689.4 192.99L1435.91 419.04L1439.46 423.02L1692.95 196.98L1689.4 192.99ZM1698.47 203.16L1444.98 429.2L1448.53 433.18L1702.02 207.14L1698.47 203.16ZM1707.53 213.33L1454.04 439.37L1457.59 443.35L1711.08 217.31L1707.53 213.33ZM1716.6 223.49L1463.11 449.54L1466.66 453.52L1720.15 227.48L1716.6 223.49Z"
                fill="white" />
            </g>
            <g opacity="0.72">
              <path opacity="0.72"
                d="M1061.4 -34.7899L956.28 58.9501L954.81 57.3L1059.93 -36.4399L1061.4 -34.7899ZM1063.69 -32.2199L958.57 61.5201L960.04 63.17L1065.16 -30.5699L1063.69 -32.2199ZM1067.45 -28.0099L962.33 65.73L963.8 67.3801L1068.92 -26.3599L1067.45 -28.0099ZM1071.21 -23.7899L966.09 69.9501L967.56 71.6L1072.68 -22.14L1071.21 -23.7899ZM1074.97 -19.5699L969.85 74.17L971.32 75.8201L1076.44 -17.9199L1074.97 -19.5699ZM1078.73 -15.3599L973.6 78.3801L975.07 80.0301L1080.19 -13.71L1078.73 -15.3599ZM1082.49 -11.14L977.36 82.6L978.83 84.2501L1083.95 -9.48993L1082.49 -11.14ZM1086.24 -6.92993L981.12 86.8101L982.59 88.4601L1087.71 -5.27997L1086.24 -6.92993ZM1090 -2.70996L984.88 91.0301L986.35 92.6801L1091.47 -1.05994L1090 -2.70996ZM1093.76 1.50006L988.64 95.2401L990.11 96.8901L1095.23 3.15009L1093.76 1.50006ZM1097.52 5.72003L992.4 99.4601L993.87 101.11L1099 7.37006L1097.52 5.72003ZM1101.28 9.94006L996.16 103.68L997.63 105.33L1102.75 11.5901L1101.28 9.94006ZM1105.04 14.1501L999.92 107.89L1001.39 109.54L1106.51 15.8L1105.04 14.1501ZM1108.8 18.3701L1003.68 112.11L1005.15 113.76L1110.27 20.0201L1108.8 18.3701ZM1112.56 22.5801L1007.44 116.32L1008.91 117.97L1114.03 24.23L1112.56 22.5801ZM1116.32 26.8L1011.2 120.54L1012.67 122.19L1117.79 28.4501L1116.32 26.8ZM1120.08 31.0201L1014.96 124.76L1016.43 126.41L1121.55 32.67L1120.08 31.0201ZM1123.84 35.23L1018.72 128.97L1020.19 130.62L1125.31 36.8801L1123.84 35.23ZM1127.6 39.4501L1022.48 133.19L1023.95 134.84L1129.07 41.1L1127.6 39.4501ZM1131.36 43.66L1026.24 137.4L1027.71 139.05L1132.83 45.3101L1131.36 43.66ZM1135.12 47.8801L1030 141.62L1031.47 143.27L1136.59 49.5301L1135.12 47.8801ZM1138.88 52.0901L1033.76 145.83L1035.23 147.48L1140.35 53.7401L1138.88 52.0901ZM1142.64 56.3101L1037.52 150.05L1038.99 151.7L1144.11 57.9601L1142.64 56.3101Z"
                fill="white" />
            </g>
            <g opacity="0.72">
              <path opacity="0.72"
                d="M1255.86 -14.55L1191.42 42.9199L1190.52 41.9099L1254.96 -15.5601L1255.86 -14.55ZM1257.26 -12.98L1192.82 44.4899L1193.72 45.4999L1258.16 -11.97L1257.26 -12.98ZM1259.56 -10.4001L1195.12 47.0699L1196.02 48.08L1260.46 -9.39008L1259.56 -10.4001ZM1261.87 -7.81006L1197.43 49.6599L1198.33 50.6699L1262.77 -6.80005L1261.87 -7.81006ZM1264.17 -5.23004L1199.73 52.2399L1200.63 53.2499L1265.07 -4.22003L1264.17 -5.23004ZM1266.48 -2.64008L1202.04 54.83L1202.94 55.84L1267.38 -1.63007L1266.48 -2.64008ZM1268.78 -0.0600586L1204.34 57.4099L1205.24 58.4199L1269.68 0.949951L1268.78 -0.0600586ZM1271.09 2.52997L1206.65 59.9999L1207.55 61.0099L1271.99 3.53992L1271.09 2.52997ZM1273.39 5.10992L1208.95 62.58L1209.85 63.59L1274.29 6.11993L1273.39 5.10992ZM1275.7 7.69995L1211.26 65.1699L1212.16 66.1799L1276.6 8.70996L1275.7 7.69995ZM1278 10.28L1213.56 67.7499L1214.46 68.7599L1278.9 11.2899L1278 10.28ZM1280.31 12.8599L1215.87 70.33L1216.77 71.34L1281.21 13.8699L1280.31 12.8599ZM1282.61 15.45L1218.17 72.9199L1219.07 73.9299L1283.51 16.46L1282.61 15.45ZM1284.92 18.03L1220.48 75.4999L1221.38 76.5099L1285.82 19.0399L1284.92 18.03ZM1287.22 20.6199L1222.78 78.09L1223.68 79.0999L1288.12 21.6299L1287.22 20.6199ZM1289.52 23.2L1225.08 80.6699L1225.98 81.6799L1290.42 24.21L1289.52 23.2ZM1291.83 25.7899L1227.39 83.2599L1228.29 84.27L1292.73 26.7999L1291.83 25.7899ZM1294.13 28.3699L1229.69 85.84L1230.59 86.8499L1295.03 29.3799L1294.13 28.3699ZM1296.44 30.96L1232 88.4299L1232.9 89.4399L1297.34 31.9699L1296.44 30.96ZM1298.74 33.5399L1234.3 91.0099L1235.2 92.02L1299.64 34.5499L1298.74 33.5399ZM1301.05 36.1199L1236.61 93.59L1237.51 94.5999L1301.95 37.1299L1301.05 36.1199ZM1303.35 38.71L1238.91 96.1799L1239.81 97.1899L1304.25 39.7199L1303.35 38.71ZM1305.66 41.2899L1241.22 98.7599L1242.12 99.77L1306.56 42.2999L1305.66 41.2899Z"
                fill="white" />
            </g>
            <g opacity="0.72">
              <path opacity="0.72"
                d="M1133.72 180.64L1069.28 238.11L1068.38 237.1L1132.82 179.63L1133.72 180.64ZM1135.13 182.21L1070.69 239.68L1071.59 240.69L1136.03 183.22L1135.13 182.21ZM1137.43 184.79L1072.99 242.26L1073.89 243.27L1138.33 185.8L1137.43 184.79ZM1139.74 187.38L1075.3 244.85L1076.2 245.86L1140.64 188.39L1139.74 187.38ZM1142.04 189.96L1077.6 247.43L1078.5 248.44L1142.94 190.97L1142.04 189.96ZM1144.35 192.55L1079.91 250.02L1080.81 251.03L1145.25 193.56L1144.35 192.55ZM1146.65 195.13L1082.21 252.6L1083.11 253.61L1147.55 196.14L1146.65 195.13ZM1148.95 197.72L1084.51 255.19L1085.41 256.2L1149.85 198.73L1148.95 197.72ZM1151.26 200.3L1086.82 257.77L1087.72 258.78L1152.16 201.31L1151.26 200.3ZM1153.56 202.88L1089.12 260.35L1090.02 261.36L1154.46 203.89L1153.56 202.88ZM1155.87 205.47L1091.43 262.94L1092.33 263.95L1156.77 206.48L1155.87 205.47ZM1158.17 208.05L1093.73 265.52L1094.63 266.53L1159.07 209.06L1158.17 208.05ZM1160.48 210.64L1096.04 268.11L1096.94 269.12L1161.38 211.65L1160.48 210.64ZM1162.78 213.22L1098.34 270.69L1099.24 271.7L1163.68 214.23L1162.78 213.22ZM1165.09 215.81L1100.65 273.28L1101.55 274.29L1165.99 216.82L1165.09 215.81ZM1167.39 218.39L1102.95 275.86L1103.85 276.87L1168.29 219.4L1167.39 218.39ZM1169.7 220.98L1105.26 278.45L1106.16 279.46L1170.6 221.99L1169.7 220.98ZM1172 223.56L1107.56 281.03L1108.46 282.04L1172.9 224.57L1172 223.56ZM1174.31 226.14L1109.87 283.61L1110.77 284.62L1175.21 227.15L1174.31 226.14ZM1176.61 228.73L1112.17 286.2L1113.07 287.21L1177.51 229.74L1176.61 228.73ZM1178.91 231.31L1114.47 288.78L1115.37 289.79L1179.81 232.32L1178.91 231.31ZM1181.22 233.9L1116.78 291.37L1117.68 292.38L1182.12 234.91L1181.22 233.9ZM1183.52 236.48L1119.08 293.95L1119.98 294.96L1184.42 237.49L1183.52 236.48Z"
                fill="white" />
            </g>
            <g opacity="0.72">
              <path opacity="0.72"
                d="M1695.73 -28L1598.56 58.65L1597.2 57.12L1694.37 -29.53L1695.73 -28ZM1697.84 -25.63L1600.67 61.02L1602.03 62.55L1699.2 -24.1L1697.84 -25.63ZM1701.32 -21.73L1604.15 64.92L1605.51 66.45L1702.68 -20.2L1701.32 -21.73ZM1704.79 -17.83L1607.62 68.82L1608.98 70.3499L1706.15 -16.3L1704.79 -17.83ZM1708.27 -13.94L1611.1 72.71L1612.46 74.24L1709.63 -12.41L1708.27 -13.94ZM1711.74 -10.04L1614.57 76.61L1615.93 78.14L1713.1 -8.51004L1711.74 -10.04ZM1715.22 -6.14005L1618.05 80.51L1619.41 82.0399L1716.58 -4.61002L1715.22 -6.14005ZM1718.69 -2.24002L1621.52 84.4099L1622.88 85.94L1720.05 -0.710052L1718.69 -2.24002ZM1722.17 1.64999L1625 88.3L1626.36 89.83L1723.53 3.17996L1722.17 1.64999ZM1725.64 5.54996L1628.47 92.2L1629.83 93.7299L1727 7.07999L1725.64 5.54996ZM1729.12 9.44998L1631.95 96.0999L1633.31 97.63L1730.48 10.9799L1729.12 9.44998ZM1732.59 13.34L1635.42 100L1636.78 101.53L1733.95 14.88L1732.59 13.34ZM1736.07 17.24L1638.9 103.89L1640.26 105.42L1737.43 18.77L1736.07 17.24ZM1739.54 21.14L1642.37 107.79L1643.73 109.32L1740.9 22.67L1739.54 21.14ZM1743.02 25.03L1645.85 111.68L1647.21 113.21L1744.38 26.56L1743.02 25.03ZM1746.49 28.93L1649.32 115.58L1650.68 117.11L1747.85 30.46L1746.49 28.93ZM1749.97 32.83L1652.8 119.48L1654.16 121.01L1751.33 34.36L1749.97 32.83ZM1753.44 36.7299L1656.27 123.38L1657.63 124.91L1754.8 38.26L1753.44 36.7299ZM1756.92 40.62L1659.75 127.27L1661.11 128.8L1758.28 42.15L1756.92 40.62ZM1760.39 44.52L1663.22 131.17L1664.58 132.7L1761.75 46.05L1760.39 44.52ZM1763.87 48.42L1666.7 135.07L1668.06 136.6L1765.23 49.95L1763.87 48.42ZM1767.34 52.31L1670.17 138.96L1671.53 140.49L1768.7 53.84L1767.34 52.31ZM1770.82 56.21L1673.65 142.86L1675.01 144.39L1772.18 57.74L1770.82 56.21Z"
                fill="white" />
            </g>
          </g>
        </g>
        <defs>
          <radialGradient id="paint0_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(318.127 -151.068) rotate(49.7963) scale(71.0636 457.262)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint1_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(817.615 -111.095) rotate(49.7868) scale(58.4228 368.674)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint2_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(441.605 153.646) rotate(49.7973) scale(45.049 211.555)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint3_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(598.444 -116.14) rotate(49.7993) scale(51.1073 290.927)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint4_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(218.228 110.109) rotate(49.7969) scale(66.6726 267.123)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint5_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(719.732 148.535) rotate(49.7962) scale(38.2908 246.384)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <linearGradient id="paint6_linear_350_144" x1="382.341" y1="-46.452" x2="492.685" y2="63.892"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="#A0F1FF" stop-opacity="0" />
            <stop offset="0.9933" stop-color="#E1A8FF" />
          </linearGradient>
          <linearGradient id="paint7_linear_350_144" x1="625.111" y1="341.247" x2="363.785" y2="79.9204"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="#A0F1FF" stop-opacity="0" />
            <stop offset="0.9933" stop-color="#E1A8FF" />
          </linearGradient>
          <linearGradient id="paint8_linear_350_144" x1="1058.43" y1="238.742" x2="727.162" y2="-92.5245"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="#A0F1FF" stop-opacity="0" />
            <stop offset="0.9933" stop-color="#E1A8FF" />
          </linearGradient>
          <linearGradient id="paint9_linear_350_144" x1="69.6043" y1="-131.909" x2="217.205" y2="15.6911"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="#A0F1FF" stop-opacity="0" />
            <stop offset="0.9933" stop-color="#E1A8FF" />
          </linearGradient>
          <radialGradient id="paint10_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(1100.13 -151.068) rotate(49.7963) scale(71.0636 457.262)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint11_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(1599.62 -111.095) rotate(49.7868) scale(58.4228 368.674)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint12_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(1223.6 153.646) rotate(49.7973) scale(45.049 211.555)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint13_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(1380.44 -116.14) rotate(49.7993) scale(51.1073 290.927)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint14_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(1000.23 110.109) rotate(49.7969) scale(66.6726 267.123)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <radialGradient id="paint15_radial_350_144" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
            gradientTransform="translate(1501.73 148.535) rotate(49.7962) scale(38.2908 246.384)">
            <stop />
            <stop offset="1" stop-opacity="0" />
          </radialGradient>
          <linearGradient id="paint16_linear_350_144" x1="1164.34" y1="-46.452" x2="1274.69" y2="63.892"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="#A0F1FF" stop-opacity="0" />
            <stop offset="0.9933" stop-color="#E1A8FF" />
          </linearGradient>
          <linearGradient id="paint17_linear_350_144" x1="1407.11" y1="341.247" x2="1145.78" y2="79.9204"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="#A0F1FF" stop-opacity="0" />
            <stop offset="0.9933" stop-color="#E1A8FF" />
          </linearGradient>
          <linearGradient id="paint18_linear_350_144" x1="1840.43" y1="238.742" x2="1509.16" y2="-92.5245"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="#A0F1FF" stop-opacity="0" />
            <stop offset="0.9933" stop-color="#E1A8FF" />
          </linearGradient>
          <linearGradient id="paint19_linear_350_144" x1="851.604" y1="-131.909" x2="999.205" y2="15.6911"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="#A0F1FF" stop-opacity="0" />
            <stop offset="0.9933" stop-color="#E1A8FF" />
          </linearGradient>
        </defs>
      </svg>
    </div>
    <div class="dashboard">
      <div class="summary">
        <i class="status-icon"></i>
        <span class="text"></span>
      </div>
      <div class="content">
        <div class="network">
          <div class="title">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 12C3 13.1819 3.23279 14.3522 3.68508 15.4442C4.13738 16.5361 4.80031 17.5282 5.63604 18.364C6.47177 19.1997 7.46392 19.8626 8.55585 20.3149C9.64778 20.7672 10.8181 21 12 21C13.1819 21 14.3522 20.7672 15.4442 20.3149C16.5361 19.8626 17.5282 19.1997 18.364 18.364C19.1997 17.5282 19.8626 16.5361 20.3149 15.4442C20.7672 14.3522 21 13.1819 21 12C21 9.61305 20.0518 7.32387 18.364 5.63604C16.6761 3.94821 14.3869 3 12 3C9.61305 3 7.32387 3.94821 5.63604 5.63604C3.94821 7.32387 3 9.61305 3 12Z" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M3.59961 9H20.3996" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M3.59961 15H20.3996" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M11.4997 3C9.81501 5.69961 8.92188 8.81787 8.92188 12C8.92188 15.1821 9.81501 18.3004 11.4997 21" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12.5 3C14.1847 5.69961 15.0778 8.81787 15.0778 12C15.0778 15.1821 14.1847 18.3004 12.5 21" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>              
            Network
          </div>
          <div class="info"></div>
        </div>
        <div class="system-loads">
          <table border="0" cellspacing="0">
            <thead>
              <tr>
                <th></th>
                <th>CPU</th>
                <th>RAM</th>
                <th>GPU</th>
                <th>VRAM</th>
                <th>DISK</th>
                <th>DB Service</th>
                <th>Model Service</th>
                <th>Crawler Service</th>
                <th>Web Service</th>
              </tr>
            </thead>
            <tbody></tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script>
    let showLoading = true;

    const localStorageKey = 'hkex_metrics';

    init();

    async function init() {
      try {
        if (showLoading) {
          setLoading(true);
        }
        const res = await window.fetch(`/api/v1/metrics?_v=${Date.now()}`);

        switch (res.status) {
          case 200: {
            const data = await res.json();
            localStorage.setItem(localStorageKey, JSON.stringify(data));
            render(data);
            break;
          }
          case 429: {
            const cachedData = JSON.parse(localStorage.getItem(localStorageKey));
            render(cachedData);
            break;
          }
          default: {
            localStorage.removeItem(localStorageKey);
            break;
          }
        }
      } catch (error) {
        localStorage.removeItem(localStorageKey);
      } finally {
        showLoading = false;
        setLoading(false);
        await new Promise((resolve) => setTimeout(resolve, 60 * 1000));
        init();
      }
    }

    function setLoading(isLoading) {
      const containerEl = document.querySelector('.container');
      const loadingEl = document.createElement('div');
      const loadingIcon = document.createElement('div');
      loadingEl.classList.add('loading');
      loadingIcon.classList.add('icon');
      loadingEl.appendChild(loadingIcon);
      if (isLoading) {
        containerEl.appendChild(loadingEl);
      } else {
        containerEl.querySelector('.loading')?.remove();
      }
    }

    function render(data) {
      const { network, system_loads } = data;

      const allItems = [network];

      function flattenItems(items) {
        items.forEach((item) => {
          if (item.label) {
            allItems.push(item);
          } else if (typeof item === 'object') {
            flattenItems(Object.values(item));
          }
        });
      }
      system_loads.forEach((sys) => {
        flattenItems(Object.values(sys));
      });

      const hasAnomalies = allItems.some(item => item.ok === false || item.color === 'yellow');

      const titleEl = document.querySelector('.dashboard > .summary');
      
      if (hasAnomalies) {
        titleEl.classList.add('error');
        titleEl.classList.remove('ok');
      } else {
        titleEl.classList.add('ok');
        titleEl.classList.remove('error');
      }
      const summaryEl = `
        <i class="status-icon"></i>
        <span class="text">${hasAnomalies ? 'Anomalies have been detected in the services.' : 'All system services are functioning normally.'}</span>
      `;
      titleEl.innerHTML = summaryEl;

      const networkEl = document.querySelector('.dashboard .network .info');
      networkEl.innerHTML = `${network.label} <i class="${network.ok ? 'icon-ok' : 'icon-error'}"></i>`;

      const tbody = document.querySelector('.dashboard table tbody');
      tbody.innerHTML = '';
      system_loads.forEach((sys, index) => {
        const tr = document.createElement('tr');
        const systemLoadsColumns = ['cpu', 'ram', 'gpu', 'vram', 'disk_usage'];
        const servicesColumns = [
          { name: 'db_service', items: ['postgresql', 'redis'] },
          { name: 'model_service', items: ['pre_processing_model', 'crude_answer_model', 'pos_neg_model'] },
          { name: 'crawler_service', items: [] },
          { name: 'web_service', items: ['front', 'worker'] },
        ];
        const td = document.createElement('td');
        const cell = `
          <div class="cell">
            <div class="name">
              <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 7C3 6.20435 3.31607 5.44129 3.87868 4.87868C4.44129 4.31607 5.20435 4 6 4H18C18.7956 4 19.5587 4.31607 20.1213 4.87868C20.6839 5.44129 21 6.20435 21 7V9C21 9.79565 20.6839 10.5587 20.1213 11.1213C19.5587 11.6839 18.7956 12 18 12H6C5.20435 12 4.44129 11.6839 3.87868 11.1213C3.31607 10.5587 3 9.79565 3 9V7Z" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 20H6C5.20435 20 4.44129 19.6839 3.87868 19.1213C3.31607 18.5587 3 17.7956 3 17V15C3 14.2044 3.31607 13.4413 3.87868 12.8787C4.44129 12.3161 5.20435 12 6 12H16.5" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M16 18C16 18.5304 16.2107 19.0391 16.5858 19.4142C16.9609 19.7893 17.4696 20 18 20C18.5304 20 19.0391 19.7893 19.4142 19.4142C19.7893 19.0391 20 18.5304 20 18C20 17.4696 19.7893 16.9609 19.4142 16.5858C19.0391 16.2107 18.5304 16 18 16C17.4696 16 16.9609 16.2107 16.5858 16.5858C16.2107 16.9609 16 17.4696 16 18Z" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 14.5V16" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 20V21.5" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M21.0324 16.25L19.7334 17" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M16.2697 19L14.9697 19.75" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M14.9697 16.25L16.2697 17" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M19.7334 19L21.0334 19.75" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M7 8V8.01" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M7 16V16.01" stroke="#6B6C6F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
              <span class="sys">${sys.node.replace(/^\w/, l => l.toUpperCase()).replace(/([a-zA-Z]+)(\d+)/, '$1 $2')}</span>
              <div class="comment-container">(<span class="comment" title="${sys.comment}">${sys.comment}</span>)</div>
            </div>
            <i class="${sys.ok === false ? 'icon-error' : ''}"></i>
          </div>`;
        td.innerHTML = cell;
        tr.appendChild(td);
        systemLoadsColumns.forEach((column) => {
          const td = document.createElement('td');
          const load = sys[column];
          const cellEl = document.createElement('span');
          cellEl.classList.add(load.color);
          if (load.ok) {
            cellEl.innerHTML = load.ratio !== null ? `${(load.ratio * 100).toFixed(0)}%` : '-';
          } else {
            const iEl = document.createElement('i');
            iEl.classList.add('icon-error');
            cellEl.appendChild(iEl);
          }
          td.appendChild(cellEl);
          tr.appendChild(td);
        });
        servicesColumns.forEach((column) => {
          const td = document.createElement('td');
          const ulEl = document.createElement('ul');
          const service = sys.services[column.name];
          
          if (column.items.length === 0) {
            if (service) {
              const liContent = genLiHtml(service);
              ulEl.innerHTML += liContent;
            }
          }
          
          column.items.forEach((item) => {
            if (service) {
              const liContent = genLiHtml(service[item]);
              ulEl.innerHTML += liContent;
            }
          });
          
          if (ulEl.innerHTML) {
            td.appendChild(ulEl);
          }
          tr.appendChild(td);
        });
        tbody.appendChild(tr);
      });
    }

    function genLiHtml(item) {
      const liEl = `
        <li>
          <div class="info">
            ${item.label}
            <i class="${item.ok ? 'icon-ok' : 'icon-error'}"></i>
          </div>
        </li>`;
      return liEl;
    }
  </script>
</body>

</html>
