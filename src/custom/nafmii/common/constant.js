export const CONFIRM_STATUS_CODE = {
  unconfirmed: 2,
  confirmed: 3,
};

export const CONFIRM_STATUS = {
  [CONFIRM_STATUS_CODE.unconfirmed]: '未确认',
  [CONFIRM_STATUS_CODE.confirmed]: '已确认',
};

export const CONFIRM_STATUS_OPTIONS = [
  {
    label: CONFIRM_STATUS[CONFIRM_STATUS_CODE.unconfirmed],
    value: CONFIRM_STATUS_CODE.unconfirmed,
  },
  {
    label: CONFIRM_STATUS[CONFIRM_STATUS_CODE.confirmed],
    value: CONFIRM_STATUS_CODE.confirmed,
  },
];

export const TASK_TYPES = ['文本识别', '关键字识别', '敏感词识别'];

export const TASK_TYPES_OPTIONS = TASK_TYPES.map((task) => ({
  label: task,
  value: task,
}));

export const TASK_TYPES_MAP = {
  文本识别: 'remark',
  关键字识别: 'keywords',
  敏感词识别: 'sensitiveWords',
};

export const COMMENT_OPERATION_MAP = {
  add: 1,
  undo: 2,
};

export const UPDATE_ANSWERS_FIELD = {
  keywords: 'keyword',
  sensitiveWords: 'sensitive_word',
};

export const UPLOAD_SENSITIVE_WORDS_FILE_MAX_SIZE = 1; //上传敏感词文件大小限制最大1MB

export const SYSTEM_LOG_TYPE_OPTIONS = [
  { label: '查看', value: 2 },
  { label: '新增', value: 3 },
  { label: '修改', value: 4 },
  { label: '删除', value: 5 },
  { label: '导出', value: 6 },
  { label: '登录', value: 7 },
  { label: '退出登录', value: 8 },
];

export const SYSTEM_LOG_STATUS_CODE = {
  success: 2,
  failed: 3,
};

export const SYSTEM_LOG_STATUS_OPTIONS = [
  {
    label: '成功',
    value: SYSTEM_LOG_STATUS_CODE.success,
  },
  {
    label: '失败',
    value: SYSTEM_LOG_STATUS_CODE.failed,
  },
];

export const KNOWLEDGE_TYPE_OPTIONS = [
  { label: '文件', value: 1 },
  { label: '词条', value: 2 },
];

export const FILE_SEARCH_DATA = [
  {
    key: 'id',
    label: '任务ID',
  },
  {
    key: 'name',
    label: '文件名称',
  },
  {
    isSelect: true,
    key: 'task_types',
    label: '任务类型',
    options: [
      {
        value: '',
        label: '全部',
      },
      ...TASK_TYPES_OPTIONS,
    ],
  },
  {
    key: 'user_name',
    label: '上传人',
  },
  {
    isSelect: true,
    key: 'confirm_status',
    label: '人工确认状态',
    options: [
      {
        value: '',
        label: '全部',
      },
      ...CONFIRM_STATUS_OPTIONS,
    ],
  },
  {
    isDatePicker: true,
    key: 'created_utc',
    label: '上传时间',
  },
];
export const FILE_SEARCH_DATA_KEYS = [
  ...FILE_SEARCH_DATA.map((item) => item.key),
  'start',
  'end',
];

export const TASK_SEARCH_DATA = [
  {
    key: 'id',
    label: '任务ID',
  },
  {
    key: 'name',
    label: '文件名称',
  },
  {
    isSelect: true,
    key: 'file_type',
    label: '文件类型',
    options: [],
  },
  {
    isSelect: true,
    key: 'task_types',
    label: '任务类型',
    options: [
      {
        value: '',
        label: '全部',
      },
      ...TASK_TYPES_OPTIONS,
    ],
  },
  {
    isSelect: true,
    key: 'confirm_status',
    label: '人工确认状态',
    options: [
      {
        value: '',
        label: '全部',
      },
      ...CONFIRM_STATUS_OPTIONS,
    ],
  },
  {
    isDatePicker: true,
    key: 'created_utc',
    label: '上传时间',
  },
];

export const TASK_SEARCH_DATA_KEYS = [
  ...TASK_SEARCH_DATA.map((item) => item.key),
  'start',
  'end',
];

export const ROUTE_NAME_MAP = {
  projectDetail: 'file',
  remark: 'label',
  task: 'task',
};
