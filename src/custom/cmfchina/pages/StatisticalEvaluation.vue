<template>
  <div class="statistics">
    <el-tabs v-model="tab">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :name="item.value"
        :label="item.label"
        :lazy="true">
        <component :is="item.component"></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { cmfchinaPermAllowed } from '../common/utils';
import StatisticsCall from '../components/StatisticsCall';
import StatisticsAccuracy from '../components/StatisticsAccuracy';
import StatisticsBusiness from '../components/StatisticsBusiness';

const tabs = [
  {
    label: '调用统计',
    value: 'call',
    component: StatisticsCall,
    perimeterAction: 'stat_call',
  },
  {
    label: '准确率统计',
    value: 'accuracy',
    component: StatisticsAccuracy,
    perimeterAction: 'stat_accuracy',
  },
  {
    label: '业务统计',
    value: 'business',
    component: StatisticsBusiness,
    perimeterAction: 'stat_business',
  },
];

export default {
  name: 'statistical-evaluation',
  components: {
    StatisticsCall,
    StatisticsAccuracy,
    StatisticsBusiness,
  },
  props: {},
  data() {
    return {
      tab: 'call',
    };
  },
  computed: {
    tabs() {
      return tabs.filter((item) => cmfchinaPermAllowed(item.perimeterAction));
    },
  },
  mounted() {
    this.tab = this.tabs[0].value;
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.statistics {
  padding: 0 20px;
  font-size: 14px;
  .el-tabs {
    padding-top: 10px;
  }
}
</style>
