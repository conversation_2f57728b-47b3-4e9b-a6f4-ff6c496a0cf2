.cmfchina-page {
  height: 100vh !important;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  > .header {
    position: sticky;
    top: 0;
    z-index: 9;
    background-color: #fff;

    .el-menu {
      min-width: 1080px;
    }
  }

  .data-panorama,
  .project-container,
  .schema-container,
  .custom-rules,
  .file-filed-container,
  .model-config-page,
  .business-group-management,
  .mail-config {
    display: flex;
    flex: 1;
    flex-flow: column;
    overflow: hidden;
    padding: 0 20px;
  }

  .statistics {
    .el-tabs__content {
      overflow: initial;
      .el-table {
        overflow: initial;
        .el-table__header-wrapper {
          position: sticky;
          top: 61px;
          z-index: 9;
        }
      }
    }
  }

  .el-pagination {
    padding: 20px 0;
    text-align: center;
  }

  .project-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .project-viewer {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    > div {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .file-viewer,
  .file-search-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;

    .detail-container {
      overflow: hidden;
    }

    .el-main {
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .file-list-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
  }

  .pdf-document-viewer {
    .annotationLayer {
      .annotation[data-subtype='Square'] {
        &.red {
          z-index: 10;
          .widget-content {
            stroke: #fc3c38 !important;
          }
        }
        .widget-content {
          stroke: #999;
          stroke-width: 2;
        }
        &.active {
          z-index: 9;
          .widget-content {
            stroke: #16a63b;
          }
          .widget-label-bg,
          .widget-label {
            display: block;
          }
        }
      }
      .widget-label-bg,
      .widget-label {
        display: none;
      }
      .widget.drawing {
        .widget-content {
          stroke: #16a63b;
        }
      }
    }
  }
}
