<template>
  <el-dialog
    title="关联法规"
    :visible="true"
    width="900px"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    @close="closeDialog"
    class="related-laws-dialog">
    <!-- 原法规信息 -->
    <div class="original-law-section">
      <h4 class="section-title">原法规信息</h4>
      <el-table :data="originalLawData" border class="original-law-table">
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          align="center"></el-table-column>
        <el-table-column
          prop="lawName"
          label="法规名称"
          width="200"></el-table-column>
        <el-table-column
          prop="ruleName"
          label="规则名称"
          width="200"></el-table-column>
        <el-table-column
          prop="content"
          label="法规内容"
          show-overflow-tooltip></el-table-column>
      </el-table>
    </div>

    <!-- 关联法规信息 -->
    <div class="related-law-section">
      <div class="section-header">
        <h4 class="section-title">关联法规信息</h4>
        <div class="law-selector">
          <span class="selector-label">选择法规</span>
          <el-select
            v-model="selectedLawId"
            placeholder="请选择法规"
            @change="handleLawChange"
            class="law-select">
            <el-option
              v-for="item in lawOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>

      <el-table
        :data="relatedLawData"
        border
        class="related-law-table"
        @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="55"
          align="center"></el-table-column>
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          align="center"></el-table-column>
        <el-table-column
          prop="ruleName"
          label="规则名称"
          width="200"></el-table-column>
        <el-table-column
          prop="content"
          label="法规内容"
          show-overflow-tooltip></el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          layout="total, prev, pager, next"
          :total="pagination.total">
        </el-pagination>
      </div>
    </div>

    <!-- 说明文字 -->
    <div class="notice-text">
      <span
        >说明：法规关联后，关联法规的规则名称会统一调整为原法规信息中的规则名称</span
      >
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel" size="small">取消关联</el-button>
      <el-button type="primary" @click="handleConfirm" size="small"
        >关联法规</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'related-laws-dialog',
  props: {
    originalLaw: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      selectedLawId: '',
      selectedRows: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 197,
      },
      lawOptions: [
        { id: 1, name: '中国人民共和国证券基金法' },
        { id: 2, name: '基金管理办法' },
        { id: 3, name: '投资者保护条例' },
      ],
      originalLawData: [
        {
          id: 3,
          lawName: '私募证券投资基金运作指引',
          ruleName: '基金类型与开放安排合规',
          content:
            '基金合同中约定的开放安排应当与私募证券投资基金的产品类型、投资策略以及资产组合的流动性等相匹配。',
        },
      ],
      relatedLawData: [
        {
          id: 49,
          ruleName: '运作方式与基金份额的合规',
          content:
            '采用封闭式运作方式的基金（以下简称封闭式基金），是指基金份额总额在基金合同期间内定不变，基金份额持有人不得申请赎回的基金；采用开放式运作方式的基金（以下简称开放式基金），是指基金份额总额不固定，基金份额可以在基金合同约定的时间和场所申购或者赎回的基金。',
        },
      ],
    };
  },
  methods: {
    closeDialog() {
      this.$emit('close');
    },
    handleLawChange(lawId) {
      // 根据选择的法规ID获取相关规则数据
      this.fetchRelatedLawData(lawId);
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchRelatedLawData(this.selectedLawId);
    },
    fetchRelatedLawData(lawId) {
      // TODO: 实际项目中这里应该调用API获取数据
      console.log('获取法规数据:', lawId);
    },
    handleCancel() {
      this.closeDialog();
    },
    handleConfirm() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要关联的法规');
        return;
      }

      // TODO: 调用关联API
      this.$emit('confirm', {
        originalLaw: this.originalLaw,
        relatedLaws: this.selectedRows,
      });

      this.$message.success('关联成功');
      this.closeDialog();
    },
  },
};
</script>

<style lang="scss" scoped>
.related-laws-dialog {
  .original-law-section,
  .related-law-section {
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0 0 16px 0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .law-selector {
      display: flex;
      align-items: center;
      gap: 8px;

      .selector-label {
        font-size: 14px;
        color: #606266;
      }

      .law-select {
        width: 200px;
      }
    }
  }

  .original-law-table,
  .related-law-table {
    width: 100%;

    ::v-deep .el-table__header {
      background-color: #f5f7fa;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }

  .notice-text {
    margin: 16px 0;
    padding: 12px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
  }

  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 8px;
    }
  }
}
</style>
