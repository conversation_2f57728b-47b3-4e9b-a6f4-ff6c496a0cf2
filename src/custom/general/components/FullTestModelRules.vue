<template>
  <div>
    <div class="header">
      <el-button @click="handleGoBack" size="medium">返回</el-button>
      <span>选择文档</span>
      <el-select
        multiple
        size="small"
        v-model="searchForm.document"
        placeholder=""
        style="width: 300px">
        <el-option
          v-for="item in typeOptions"
          :key="item.id"
          :value="item.id"
          :label="item.name"></el-option>
      </el-select>
      <span>选择法规</span>
      <el-select
        multiple
        size="small"
        v-model="searchForm.document"
        placeholder=""
        style="width: 300px">
        <el-option
          v-for="item in typeOptions"
          :key="item.id"
          :value="item.id"
          :label="item.name"></el-option>
      </el-select>
      <el-button type="primary" size="medium">开始测试</el-button>
      <el-button type="primary" size="medium">提交审核</el-button>
    </div>
    <el-table :data="tableData" ref="table" class="rule-list-table has-border">
      <el-table-column
        width="100"
        prop="id"
        label="ID"
        header-align="center"></el-table-column>
      <el-table-column
        width="100"
        prop="id"
        label="法规名称"
        header-align="center"></el-table-column>
      <el-table-column
        width="100"
        prop="id"
        label="规则名称"
        header-align="center"></el-table-column>
      <el-table-column
        width="100"
        prop="id"
        label="法规原文"
        header-align="center"></el-table-column>
      <el-table-column
        width="100"
        prop="id"
        label="核心要求"
        header-align="center"></el-table-column>
      <el-table-column
        width="100"
        prop="id"
        label="验证方式"
        header-align="center"></el-table-column>
      <el-table-column width="100" prop="id" align="center">
        <template slot="header" slot-scope="{}">
          <el-popover
            placement="bottom"
            width="160"
            popper-class="model-rule-column-select-popper"
            trigger="click">
            <div
              @click="handleStatusAllClick"
              class="option-item"
              :class="{
                'is-active': filterParams.status.length === columnStatusNums,
              }">
              <span>全部</span>
              <i class="el-icon-check"></i>
            </div>
            <div
              v-for="(value, key) in ROW_STATUS_MAP"
              :key="value"
              class="option-item"
              :class="{
                'is-active': filterParams.status.includes(key),
              }"
              @click="handleLawStatusItemClick(key)">
              <span>{{ value }}</span>
              <i class="el-icon-check"></i>
            </div>
            <span slot="reference" class="option-header">
              是否合规<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <div>{{ ROW_STATUS_MAP[scope.row.status] }}</div>
        </template>
      </el-table-column>
      <el-table-column width="200" label="操作" header-align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.row)"
            >详情</el-button
          >
          <el-button type="text" @click="handleRelate(scope.row)"
            >关联法规</el-button
          >
          <el-button type="text" @click="handleReTest(scope.row)"
            >重新测试</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="total, prev, pager, next, sizes"
      :page-sizes="[10, 20, 50]"
      :current-page="searchForm.page"
      :page-size="searchForm.size"
      :total="pager.total"
      @current-change="handleChangePage"
      @size-change="handleChangeSize">
    </el-pagination>
    <related-laws-dialog v-if="showRelatedLawsDialog"></related-laws-dialog>
  </div>
</template>
<script>
import RelatedLawsDialog from './RelatedLawsDialog.vue';
export default {
  name: 'full-test-model-rules',
  components: {
    RelatedLawsDialog,
  },
  props: {
    typeOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      searchForm: {
        document: '',
        rule: '',
        page: 1,
        size: 10,
      },
      pager: {
        total: 0,
      },
      tableData: [
        {
          id: 1,
        },
      ],
      ROW_STATUS_MAP: {
        1: '合规',
        2: '不合规',
        3: '不涉及',
        4: '分析中',
      },
      filterParams: {
        status: [],
      },
      showRelatedLawsDialog: false,
    };
  },
  computed: {
    columnStatusNums() {
      return Object.keys(this.ROW_STATUS_MAP).length;
    },
  },
  methods: {
    handleStatusAllClick() {
      if (this.filterParams.status.length < this.lwaStatusNums) {
        this.filterParams.status = Object.keys(this.LAW_STATUS_MAP);
      } else {
        this.filterParams.status = [];
      }
      this.handleSearchClick();
    },
    handleChangePage(page) {
      this.searchForm.page = page;
      //   this.getLaws();
    },
    handleChangeSize(size) {
      this.searchForm.page = 1;
      this.searchForm.size = size;
      //   this.getLaws();
    },
    handleSearchClick() {
      this.searchForm.page = 1;
      // todo
      //   this.getLaws();
    },
    handleLawStatusItemClick(val) {
      const isExist = this.filterParams.status.includes(val);
      if (isExist) {
        this.filterParams.status = this.filterParams.status.filter((item) => {
          return item !== val;
        });
      } else {
        this.filterParams.status.push(val);
      }
      this.handleSearchClick();
    },
    handleLawStatusAllClick() {
      if (this.filterParams.status.length < this.columnStatusNums) {
        this.filterParams.status = Object.keys(this.ROW_STATUS_MAP);
      } else {
        this.filterParams.status = [];
      }
      this.handleSearchClick();
    },
    handleGoBack() {
      this.$emit('goBack');
    },
    handleDetail(row) {
      this.$emit('detail', row);
    },
    handleRelate(row) {
      console.log(row);
      this.showRelatedLawsDialog = true;
    },
    handleReTest(row) {
      this.$emit('reTest', row);
    },
  },
};
</script>
<style lang="scss" scoped>
.header {
  display: flex;
  margin: 20px;
  column-gap: 20px;
  align-items: center;
}
</style>
<style lang="scss">
.el-popover.model-rule-column-select-popper {
  padding: 10px 0;
  max-height: 388px;
  overflow-y: auto;
  .option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    padding: 0 20px;
    cursor: pointer;

    &:hover {
      background: #f3f7fc;
      color: $--color-primary;
    }

    .el-icon-check {
      visibility: hidden;
      color: $--color-primary;
    }

    &.is-active {
      background: #f3f7fc;
      color: $--color-primary;
      .el-icon-check {
        visibility: visible;
      }
    }
  }
}
</style>
