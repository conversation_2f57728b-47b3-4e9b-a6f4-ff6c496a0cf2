<template>
  <el-dialog
    title="测试"
    width="600px"
    :visible="dialogVisible"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    @close="closeDialog">
    <div>
      <el-form
        ref="form"
        class="test-rule-form"
        :model="form"
        label-width="80px"
        label-position="left">
        <el-form-item label="选择文档">
          <div class="document-select">
            <el-select
              v-model="form.document_ids"
              multiple
              collapse-tags
              clearable
              size="small"
              placeholder="请选择一份解析完成的文档"
              style="width: 100%">
              <el-option
                v-for="item in documentList"
                :key="item.id"
                :value="item.id"
                :label="item.name"></el-option>
            </el-select>
            <theme-icon
              class="test-icon"
              name="test-law"
              icon-class="el-icon-edit"
              @click.native.stop="handleTestLawRule"></theme-icon>
          </div>
          <div class="tips">提示：一次性最多选择 5 份文档进行测试</div>
        </el-form-item>
      </el-form>
      <div class="test-result-container">
        <div class="test-result">测试结果</div>
        <template v-if="testResult.length">
          <div class="test-switch">
            <i class="el-icon-caret-left"></i>
            <div class="test-switch-title">基金合同</div>
            <i class="el-icon-caret-right"></i>
          </div>
          <div class="test-content">
            <div class="test-content-title">提取内容</div>
            <div class="test-content-text">
              内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容
              内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容
            </div>
          </div>
        </template>
        <div class="empty" v-else>请在上方选择文档</div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closeDialog" size="small">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'test-rule-dialog',
  props: {
    documentList: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
  },
  data() {
    return {
      form: {
        document_ids: [],
      },
      testResult: [],
      dialogVisible: false,
    };
  },
  methods: {
    handleTestLawRule() {
      // todo
    },
    closeDialog() {
      this.$emit('close');
    },
  },
};
</script>
<style lang="scss" scoped>
.document-select {
  display: flex;
}
.tips {
  line-height: 20px;
  font-size: 12px;
  color: #b14435;
}
.test-rule-form {
  padding: 0 40px;
}
.test-result-container {
  position: relative;
  border: 1px solid #e9e5e5;
  border-radius: 6px;
  padding: 10px 20px;
  min-height: 160px;
  .empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: #ccc;
  }
}
.test-result {
  font-size: 16px;
  font-weight: bold;
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
}
.test-switch {
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 10px;
  font-size: 16px;
  .test-switch-title {
    max-width: 300px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  [class^='el-icon-'] {
    font-size: 24px;
    cursor: pointer;
    color: #225476;
  }
}
.test-content {
  display: flex;
  align-items: center;
  column-gap: 20px;
  .test-content-title {
    flex-shrink: 0;
  }
  .test-content-text {
    border: 1px solid #e9e5e5;
    border-radius: 6px;
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
    padding: 12px 10px;
    margin-bottom: 20px;
  }
}
.test-icon {
  cursor: pointer;
  width: 32px;
  margin-left: 10px;
}
</style>
