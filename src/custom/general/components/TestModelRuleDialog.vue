<template>
  <el-dialog
    title="测试"
    width="600px"
    :visible="true"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    @close="closeDialog">
    <div>
      <el-form
        ref="form"
        class="test-rule-form"
        :model="form"
        label-width="80px"
        label-position="left">
        <el-form-item label="选择文档">
          <div class="document-select">
            <el-select
              v-model="form.document_ids"
              clearable
              size="small"
              placeholder="请选择一份解析完成的文档"
              style="width: 100%">
              <el-option
                v-for="item in documentList"
                :key="item.id"
                :value="item.id"
                :label="item.name"></el-option>
            </el-select>
            <theme-icon
              class="test-icon"
              name="test-law"
              icon-class="el-icon-edit"
              @click.native.stop="handleTestLawRule"></theme-icon>
          </div>
        </el-form-item>
      </el-form>
      <div class="test-result-container">
        <div class="test-result">测试结果</div>
        <template v-if="true">
          <div class="result-list">
            <div class="result-item">
              <div class="result-label">法规原文</div>
              <div class="result-content">
                基金合同中应当约定不少于3个月的份额锁定期或者与基金份额持有期限对应的短期赎回费用安排，收取的赎回费用应当归属基金财产。
              </div>
            </div>
            <div class="result-item">
              <div class="result-label">合同片段</div>
              <div class="result-content">
                基金合同中应当约定不少于3个月的份额锁定期
              </div>
            </div>
            <div class="result-item">
              <div class="result-label">是否合规</div>
              <div class="result-content">合规</div>
            </div>
            <div class="result-item">
              <div class="result-label">判定依据</div>
              <div class="result-content">基金合同中包含"锁定期≥3个月</div>
            </div>
            <div class="result-item">
              <div class="result-label">修改意见</div>
              <div class="result-content">无需要修改。</div>
            </div>
          </div>
        </template>
        <div class="empty" v-else>
          <p>请在上方选择文档</p>
          <p>文档选择完成后，请点击开始测试</p>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closeDialog" size="small">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'test-rule-dialog',
  props: {
    documentList: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      form: {
        document_ids: [],
      },
      testResult: [],
    };
  },
  methods: {
    handleTestLawRule() {
      // todo
    },
    closeDialog() {
      this.$emit('close');
    },
  },
};
</script>
<style lang="scss" scoped>
.document-select {
  display: flex;
}
.tips {
  line-height: 20px;
  font-size: 12px;
  color: #b14435;
}
.test-rule-form {
  padding: 0 40px;
}
.test-result-container {
  position: relative;
  border: 1px solid #e9e5e5;
  border-radius: 6px;
  padding: 20px;
  min-height: 160px;

  .empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: #ccc;
    text-align: center;
  }

  .result-list {
    margin-top: 10px;
  }

  .result-item {
    display: flex;
    margin-bottom: 20px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .result-label {
    width: 80px;
    flex-shrink: 0;
    font-weight: 500;
    color: #333;
    font-size: 14px;
    line-height: 1.5;
    margin-right: 20px;
  }

  .result-content {
    flex: 1;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    word-break: break-all;
    border-radius: 4px;
    border: 1px solid #e9e5e5;
    padding: 6px;
  }
}
.test-result {
  font-size: 16px;
  font-weight: bold;
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.test-icon {
  cursor: pointer;
  width: 32px;
  margin-left: 10px;
}
</style>
