<template>
  <el-tooltip
    :disabled="!isShowTooltip"
    :content="showSuffixEllipsis ? `${content}${suffix}` : content"
    :placement="placement"
    :enterable="enterable"
    popper-class="tooltip-over-popper">
    <div
      class="tooltip-content"
      ref="container"
      :style="{ justifyContent: justifyContent }">
      <slot name="before"></slot>
      <p
        ref="parent"
        class="over-flow"
        :style="parentStyle"
        @mouseover="onMouseOver">
        <span ref="content">
          {{ showSuffixEllipsis ? `${content}${suffix}` : content }}
        </span>
      </p>
      <slot></slot>
      <span
        v-if="(showSuffixEllipsis && isContentEllipsis) || !showSuffixEllipsis">
        {{ suffix }}
      </span>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  name: 'tooltipOver',
  props: {
    content: {
      type: String,
      default: '',
    },
    suffix: {
      type: String,
      default: '',
    },
    // 未传入width时，读取父dom宽度，不能有padding样式
    width: {
      type: Number,
      default: 0,
    },
    maxWidth: {
      type: Number,
      default: 0,
    },
    placement: {
      type: String,
      default: 'top',
    },
    justifyContent: {
      type: String,
      default: 'normal',
    },
    enterable: {
      type: Boolean,
      default: true,
    },
    // 是否溢出隐藏时，展示suffix内容
    showSuffixEllipsis: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShowTooltip: false,
      containerWidth: 0,
      contentWidth: 0,
      isContentEllipsis: false,
    };
  },
  computed: {
    parentStyle() {
      const style = {};
      if (this.width) {
        style.width = `${this.width}px`;
      }
      if (this.maxWidth) {
        style.maxWidth = `${this.maxWidth}px`;
      }
      return style;
    },
  },
  async mounted() {
    // 等待获取 ContentWidth
    await this.$nextTick();
    this.getContainerWidth();
    this.getContentWidth();
    this.updateEllipsisStatus();
  },
  methods: {
    updateEllipsisStatus() {
      if (this.contentWidth > this.containerWidth) {
        this.isContentEllipsis = true;
      } else {
        this.isContentEllipsis = false;
      }
      this.$emit('is-ellipsis', this.isContentEllipsis);
    },
    getContainerWidth() {
      if (this.width) {
        this.containerWidth = this.width;
      } else {
        const parentWidth = this.$refs.container.parentNode.offsetWidth;
        const parentChildrenNodes = this.$refs.container.parentNode.children;
        let otherChildrenWidth = 0;
        let slotDomWidth = 0;
        if (parentChildrenNodes.length > 1) {
          Array.from(parentChildrenNodes).forEach((node) => {
            if (node !== this.$refs.container) {
              const nodeWidth =
                node.offsetWidth + this.getNodeMarginWidth(node);
              otherChildrenWidth += nodeWidth;
            }
          });
        }

        if (Object.keys(this.$slots).length > 0) {
          Object.keys(this.$slots).forEach((key) => {
            if (this.$slots[key]) {
              const slotInstance = Array.isArray(this.$slots[key])
                ? this.$slots[key][0]
                : this.$slots[key];
              const slotNode = slotInstance.elm;
              if (slotNode) {
                slotDomWidth +=
                  slotNode.offsetWidth + this.getNodeMarginWidth(slotNode);
              }
            }
          });
        }

        const tipContainerMargin = this.getNodeMarginWidth(
          this.$refs.container,
        );
        this.containerWidth =
          parentWidth - otherChildrenWidth - slotDomWidth - tipContainerMargin;
      }
    },
    getNodeMarginWidth(node) {
      const style = window.getComputedStyle(node);
      const margin =
        parseFloat(style.marginLeft) + parseFloat(style.marginRight);
      return margin;
    },
    getContentWidth() {
      this.contentWidth = this.$refs.content.offsetWidth;
    },
    onMouseOver() {
      this.getContainerWidth();
      this.getContentWidth();
      if (this.contentWidth > this.containerWidth) {
        this.isShowTooltip = true;
      } else {
        this.isShowTooltip = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.over-flow {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: fit-content;
  margin: 0;
}
.tooltip-content {
  min-width: 0;
  flex: 1;
  display: flex;
  align-items: center;
}
</style>
<style lang="scss">
.tooltip-over-popper {
  max-width: 600px;
}
</style>
