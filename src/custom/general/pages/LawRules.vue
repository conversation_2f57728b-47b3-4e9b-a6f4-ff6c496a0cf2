<template>
  <div class="law-rules" v-loading="isLoading">
    <div class="header">
      <div class="header-button">
        <el-button size="medium" type="primary" @click="handleGoBack">
          <i class="fa fa-arrow-left fa-fw"></i>
          返回
        </el-button>
        <el-button size="medium" type="primary" @click="handleAddLawRule">
          新增法规
        </el-button>
        <el-button
          size="medium"
          type="primary"
          @click="handleRuleTransformation">
          规则转换
        </el-button>
      </div>
      <tooltip-over
        class="law-title"
        :content="lawInfo.name"
        justifyContent="center"></tooltip-over>
      <div class="search">
        <el-input
          ref="inputRef"
          v-model.trim="searchForm.keywords"
          placeholder="请输入关键字"
          size="medium"
          clearable
          class="search-input"
          @clear="handleSearchClick"
          @keydown.enter.native="handleSearchClick">
        </el-input>
        <el-button
          type="primary"
          size="medium"
          class="search-btn"
          @click="handleSearchClick">
          查询
        </el-button>
      </div>
    </div>
    <el-table
      :data="lawRules"
      v-loading="isTableLoading"
      height="calc(100vh - 191px)">
      <el-table-column width="100" prop="id" label="ID"></el-table-column>
      <el-table-column
        prop="content"
        label="法规明细"
        min-width="400"></el-table-column>
      <el-table-column
        prop="scenariosText"
        label="应用场景"
        min-width="100"
        align="center"
        show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.scenariosText || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="content"
        label="规则转换状态"
        width="130"
        align="center">
        <template slot-scope="scope">
          <span
            :class="{
              error: scope.row.status === LAW_RULE_STATUS.CONVERT_FAILED,
            }"
            >{{ LAW_RULE_STATUS_MAP[scope.row.status] }}</span
          >
          <i
            v-if="
              [
                LAW_RULE_STATUS.CONVERTED,
                LAW_RULE_STATUS.CONVERT_FAILED,
              ].includes(scope.row.status)
            "
            class="el-icon-refresh-right refresh-icon"
            @click="handleRefreshRule(scope.row)"></i>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" header-align="center">
        <template slot-scope="scope">
          <div class="operation">
            <el-tooltip effect="dark" content="审核开关" placement="top">
              <el-switch
                v-model="scope.row.enable"
                @change="handleSwitchChange(scope.row)">
              </el-switch>
            </el-tooltip>
            <el-tooltip effect="dark" content="开始测试" placement="top">
              <el-button
                :type="!$platform.isDefaultEnv() ? 'primary' : 'text'"
                size="small"
                circle
                @click.native.stop="handleTestLawRule(scope.row)">
                <theme-icon
                  class="test-icon"
                  name="test-law"
                  icon-class="el-icon-edit"></theme-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip effect="dark" content="修改法规明细" placement="top">
              <el-button
                :type="!$platform.isDefaultEnv() ? 'primary' : 'text'"
                size="small"
                circle
                @click.native.stop="handleEditLawRule(scope.row)">
                <theme-icon
                  name="edit-law"
                  icon-class="el-icon-edit"></theme-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip effect="dark" content="删除法规明细" placement="top">
              <el-button
                :type="!$platform.isDefaultEnv() ? 'danger' : 'text'"
                size="small"
                circle
                @click.native.stop="handleDeleteLawRule(scope.row)">
                <theme-icon
                  name="delete-law"
                  icon-class="el-icon-delete"></theme-icon>
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="total, prev, pager, next, sizes"
      :page-sizes="[10, 20, 50]"
      :current-page="pager.page"
      :page-size="pager.size"
      :total="pager.total"
      @current-change="handleChangePage"
      @size-change="handleChangeSize">
    </el-pagination>
    <el-dialog
      title="提示"
      :visible.sync="ruleTransformationDialogVisible"
      width="300px"
      class="rule-transformation-dialog"
      :close-on-click-modal="false"
      :modal-append-to-body="false">
      <div class="dialog-tips">请确认下面的法规是否需要重新转换</div>
      <el-checkbox-group
        v-model="ruleTransFormationData"
        class="rule-transformation-checkbox-group">
        <el-checkbox :label="LAW_RULE_STATUS.CONVERTED"
          >转换成功的法规</el-checkbox
        >
        <el-checkbox :label="LAW_RULE_STATUS.CONVERT_FAILED"
          >转换失败的法规</el-checkbox
        >
        <el-checkbox :label="LAW_RULE_STATUS.INIT">未转换的法规</el-checkbox>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelTransfromDialog" size="small">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirmTransfromDialog"
          size="small">
          开始转换
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="switchDialogVisible"
      width="300px"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      @close="closeSwitchDialog">
      <div class="dialog-tips">删除/关闭对应的审核规则？</div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelSwitchRule" size="small">
          取消
        </el-button>
        <el-button type="primary" @click="handleDeleteSwitchRule" size="small">
          删除
        </el-button>
        <el-button type="primary" @click="handleOffRule" size="small">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <add-law-rule-dialog
      v-if="addLawRuleDialogVisible"
      :law-name="lawInfo.name"
      :current-rule="currentRule"
      :type-options="currentlawTypeOptions"
      @close="handleCloseAddLawRuleDialog"
      @confirm="handleAddLawRuleConfirm"></add-law-rule-dialog>
    <test-rule-dialog
      :visible="testRuleDialogVisible"
      :test-rule-info="testRuleInfo"
      @close="handleCloseTestRuleDialog"></test-rule-dialog>
    <check-diff-rules-dialog
      ref="checkDiffRulesDialog"
      v-if="checkDiffRulesDialogVisible"
      :diff-rules="diffRules"
      :law-name="lawInfo.name"
      @cancel="handleCancelCheckDiffRulesDialog"
      @update="handleUpdateLawRule"></check-diff-rules-dialog>
  </div>
</template>
<script>
import { laws as lawsApi } from '@/store/api';
import AddLawRuleDialog from '../components/AddLawRuleDialog.vue';
import TestRuleDialog from '../components/TestRuleDialog.vue';
import CheckDiffRulesDialog from '../components/CheckDiffRulesDialog.vue';
import TooltipOver from '../components/TooltipOver.vue';
import {
  LAW_SESSION_KEY,
  LAW_RULE_STATUS,
  LAW_RULE_STATUS_MAP,
  LAW_REFRESH_STATUS,
} from '@/store/constants';
import { polling } from '@/utils';

export default {
  components: {
    AddLawRuleDialog,
    TestRuleDialog,
    CheckDiffRulesDialog,
    TooltipOver,
  },
  name: 'LawRules',
  props: {
    rankId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      LAW_RULE_STATUS,
      LAW_RULE_STATUS_MAP,
      searchForm: {
        keywords: '',
      },
      lawRules: [],
      lawInfo: {},
      pager: {
        page: 1,
        size: 10,
        total: 0,
      },
      addLawRuleDialogVisible: false,
      ruleTransformationDialogVisible: false,
      testRuleDialogVisible: false,
      switchDialogVisible: false,
      ruleTransFormationData: [],
      currentRule: null,
      lawTypeOptions: [],
      needConfirmData: {},
      testRuleInfo: {},
      closePolling: null,
      isLoading: true,
      isTableLoading: false,
      diffRules: [],
      checkDiffRulesDialogVisible: false,
    };
  },
  computed: {
    params() {
      return {
        page: this.pager.page,
        size: this.pager.size,
        ...this.searchForm,
      };
    },
    currentlawTypeOptions() {
      return this.lawTypeOptions.filter((item) =>
        this.lawInfo.scenarios.map((item) => item.id).includes(item.id),
      );
    },
    lawId() {
      return this.lawInfo.id;
    },
  },
  methods: {
    async init() {
      this.getScenarios();
      this.getLawRules();
      this.getLawInfo();
    },
    openPolling() {
      this.closePolling = polling(
        async () => {
          if (this.needFreshRules()) {
            await this.getLawRules({ loading: false });
          } else {
            this.closePolling();
          }
        },
        5e3,
        { leading: false },
      );
    },
    needFreshRules() {
      return this.lawRules.some(
        (item) =>
          item.status === LAW_RULE_STATUS.CONVERTING ||
          item.status === LAW_RULE_STATUS.WAITING,
      );
    },
    async getScenarios() {
      try {
        this.lawTypeOptions = await lawsApi.getScenarios();
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },
    async getLawInfo() {
      try {
        const lawInfo = await lawsApi.getLawInfo(this.rankId);
        this.lawInfo = lawInfo;
        if (lawInfo.refresh_status === LAW_REFRESH_STATUS.SUCCESS) {
          this.getDiffRules();
        } else if (lawInfo.refresh_status === LAW_REFRESH_STATUS.FAILED) {
          await lawsApi.revertLawRules(this.lawId);
        }
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        this.isLoading = false;
      }
    },
    async getDiffRules() {
      try {
        const { diff } = await lawsApi.getDiffRules(this.lawId);
        this.diffRules = diff.filter((item) => item.type !== 'equal');
        this.checkDiffRulesDialogVisible = true;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },
    async getLawRules(options = { loading: true }) {
      try {
        if (options.loading) {
          this.isTableLoading = true;
        }
        if (this.closePolling) {
          this.closePolling();
        }
        const { items, total } = await lawsApi.getLawRules(
          this.rankId,
          this.params,
        );
        this.openPolling();
        this.lawRules = items.map((item) => {
          return {
            ...item,
            scenariosText: item.scenarios.map((item) => item.name).join('、'),
          };
        });
        this.pager.total = total;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        if (options.loading) {
          this.isTableLoading = false;
        }
      }
    },
    handleCloseTestRuleDialog() {
      this.testRuleDialogVisible = false;
      this.testRuleInfo = {};
    },
    closeSwitchDialog() {
      this.needConfirmData = null;
    },
    handleCancelSwitchRule() {
      const row = this.lawRules.find(
        (item) => item.id === this.needConfirmData.id,
      );
      row.enable = !row.enable;
      this.switchDialogVisible = false;
    },
    handleDeleteSwitchRule() {
      this.handleDeleteLawRule(this.needConfirmData, { confirm: false });
      this.switchDialogVisible = false;
    },
    async handleAddLawRuleConfirm(data) {
      this.addLawRuleDialogVisible = false;
      try {
        if (this.currentRule) {
          await lawsApi.updateRule(this.lawId, this.currentRule.id, data);
        } else {
          await lawsApi.createRule(this.lawId, data);
        }
        this.$notify({
          title: '成功',
          message: this.currentRule ? '更新法规明细成功' : '新建法规明细成功',
          type: 'success',
        });
        this.getLawRules();
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
        });
      } finally {
        this.currentRule = null;
      }
    },
    handleCloseAddLawRuleDialog() {
      this.currentRule = null;
      this.addLawRuleDialogVisible = false;
    },
    handleTestLawRule(row) {
      this.testRuleDialogVisible = true;
      this.testRuleInfo = row;
    },
    async handleRefreshRule(row) {
      const confirm = await this.$confirm('是否要重新转换该条规则？', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
      }).catch(() => {});
      if (confirm === 'confirm') {
        try {
          await lawsApi.ruleConvert(row.id);
          this.$notify({
            title: '成功',
            message: '重新转换成功',
            type: 'success',
          });
          this.getLawRules();
        } catch (error) {
          this.$notify({
            title: '错误',
            message: error.message,
            type: 'error',
          });
        }
      }
    },
    async handleDeleteLawRule(row, options = { comfirm: true }) {
      if (options.comfirm) {
        const { exists_ids } = await lawsApi.getCheckPointsExistsIdsByRule(
          row.id,
        );
        const confirm = await this.$msgbox({
          title: '提示',
          message:
            exists_ids.length > 0
              ? `<div>
          <p style="font-size: 14px;  color: #303133; margin-bottom: 10px;">是否删除该条法规明细?</p>
          <p style="color: #F56C6C; font-size: 12px;">提示：法规明细删除后，对应的审核规则将被废弃</p>
        </div>`
              : `<div>
          <p style="font-size: 14px;  color: #303133; margin-bottom: 10px;">是否删除该条法规明细?</p>
        </div>`,
          showCancelButton: true,
          confirmButtonText: '是',
          cancelButtonText: '否',
          dangerouslyUseHTMLString: true,
        }).catch(() => {});
        if (confirm != 'confirm') {
          return;
        }
      }

      try {
        await lawsApi.deleteRule(this.lawId, row.id);
        if (this.lawRules.length === 1 && this.pager.page > 1) {
          this.pager.page--;
        }
        this.getLawRules();
        this.$notify({
          title: '成功',
          message: '删除法规明细成功',
          type: 'success',
        });
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },
    handleOffRule() {
      const rowData = this.lawRules.find(
        (item) => item.id === this.needConfirmData.id,
      );
      this.updateRuleEnable(rowData);
      this.switchDialogVisible = false;
    },
    async handleSwitchChange(row) {
      if (!row.enable && row.status === LAW_RULE_STATUS.CONVERTED) {
        this.needConfirmData = row;
        this.switchDialogVisible = true;
      } else {
        this.updateRuleEnable(row);
      }
    },
    async updateRuleEnable(row) {
      const law_id = this.lawId;
      const rule_id = row.id;
      try {
        await lawsApi.switchRule(law_id, rule_id, {
          enable: row.enable,
        });
        this.$notify({
          title: '成功',
          message: row.enable ? '启用成功' : '禁用成功',
          type: 'success',
        });
        this.getLawRules({ loading: false });
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
        row.enable = !row.enable;
      }
    },
    handleChangePage(page) {
      this.pager.page = page;
      this.getLawRules();
    },
    handleChangeSize(size) {
      this.pager.page = 1;
      this.pager.size = size;
      this.getLawRules();
    },
    handleGoBack() {
      let query = {};
      try {
        query = JSON.parse(window.sessionStorage.getItem(LAW_SESSION_KEY));
      } catch (error) {
        console.error(error);
      }
      this.$router.replace({
        query,
        name: 'laws',
      });
    },
    handleAddLawRule() {
      this.addLawRuleDialogVisible = true;
    },
    handleRuleTransformation() {
      this.ruleTransformationDialogVisible = true;
    },
    handleCancelTransfromDialog() {
      this.ruleTransformationDialogVisible = false;
    },
    async handleConfirmTransfromDialog() {
      try {
        await lawsApi.convertLawRules(this.lawId, {
          status: this.ruleTransFormationData,
        });
        this.ruleTransformationDialogVisible = false;
        this.ruleTransFormationData = [];
      } catch (error) {
        if (error.message) {
          this.$notify({
            title: '错误',
            message: error.message,
            type: 'error',
          });
        }
      } finally {
        this.getLawRules();
      }
    },
    handleSearchClick() {
      this.pager.page = 1;
      this.getLawRules();
    },
    handleEditLawRule(row) {
      this.currentRule = row;
      this.addLawRuleDialogVisible = true;
    },
    async handleUpdateLawRule(data) {
      try {
        await lawsApi.applyLawRules(this.lawId, data);
        this.$notify({
          title: '成功',
          message: '更新成功',
          type: 'success',
        });
        this.getLawRules();
        this.checkDiffRulesDialogVisible = false;
      } catch (error) {
        this.$refs.checkDiffRulesDialog.closeLoading();
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },
    async handleCancelCheckDiffRulesDialog() {
      try {
        await lawsApi.revertLawRules(this.lawId);
        this.checkDiffRulesDialogVisible = false;
      } catch (error) {
        this.$refs.checkDiffRulesDialog.closeLoading();
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },
  },
  created() {
    this.init();
  },
};
</script>
<style lang="scss" scoped>
.law-rules {
  .el-pagination {
    padding: 20px 0;
    text-align: center;
  }
  .operation {
    display: flex;
    align-items: center;
    .test-icon {
      margin-left: 10px;
    }
  }
}
.law-title {
  text-align: center;
  line-height: 60px;
  height: 60px;
  font-size: 24px;
  font-weight: bold;
  flex: 1;
}
.header {
  min-height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  column-gap: 20px;
  padding: 0 20px;
  .header-button {
    flex-shrink: 0;
  }
  .search {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    column-gap: 10px;
  }
}
.refresh-icon {
  margin-left: 10px;
  cursor: pointer;
  font-size: 16px;
}
.error {
  color: red;
}
</style>
<style lang="scss">
.rule-transformation-dialog {
  .dialog-tips {
    font-weight: bold;
    margin-bottom: 10px;
  }
  .rule-transformation-checkbox-group {
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    row-gap: 10px;
  }
}
</style>
