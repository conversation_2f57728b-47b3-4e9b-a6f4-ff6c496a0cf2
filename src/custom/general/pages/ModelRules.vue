<template>
  <div class="custom-rules" v-loading="isLoading">
    <template v-if="!isShowingFullTest">
      <div class="page-header operater-list">
        <div class="operater-list-left">
          <slot name="operater"></slot>
          <el-button size="medium" type="primary" @click="handleOpenFullTest">
            <div class="test-icon-wrapper">
              <theme-icon
                class="test-icon"
                name="test-law-white"
                icon-class="el-icon-edit"></theme-icon>
              完整测试
            </div>
          </el-button>
        </div>
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="法规实行状态：">
            <el-checkbox-group
              v-model="searchForm.abandoned"
              @change="handleSearchClick"
              style="line-height: 40px">
              <el-checkbox :label="ABANDONED_MAP.NOT">现行</el-checkbox>
              <el-checkbox :label="ABANDONED_MAP.YES">废弃</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="searchForm.field"
              size="medium"
              class="search-field"
              @change="handleSearchFieldChange">
              <el-option
                v-for="(item, index) in searchOptions"
                :key="index"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
            <el-input
              ref="inputRef"
              v-model.trim="searchForm.keyword"
              :placeholder="currentSearchOption.placeholder"
              size="medium"
              clearable
              class="search-input"
              @clear="handleSearchClick"
              @keydown.enter.native="handleSearchClick">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="medium"
              class="search-btn"
              @click="handleSearchClick">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table
        :data="tableData"
        ref="table"
        class="rule-list-table has-border"
        height="calc(100vh - 228px)">
        <el-table-column width="100" prop="id" label="ID"></el-table-column>
        <el-table-column min-width="100" align="center" label="应用场景">
          <template slot-scope="scope">
            <!-- TODO: 后续看是否支持编辑 -->
            <div>{{ scope.row.scenario_names.join('、') }}</div>
          </template>
        </el-table-column>
        <el-table-column
          min-width="100"
          align="center"
          prop="order.name"
          label="法规名称"></el-table-column>
        <el-table-column
          min-width="300"
          header-align="center"
          prop="nowData.rule_content"
          label="法规内容">
          <template slot-scope="scope">
            <div>
              {{ scope.row.nowData.rule_content }}
              <el-tooltip :content="scope.row.abandoned_reason" placement="top">
                <el-tag type="danger" size="mini" v-show="scope.row.abandoned">
                  废弃
                </el-tag>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          min-width="100"
          prop="nowData.name"
          align="center"
          label="规则名称"></el-table-column>
        <el-table-column
          min-width="300"
          prop="nowData.core"
          header-align="center"
          label="核心要求"></el-table-column>
        <el-table-column
          min-width="400"
          prop="nowData.check_method"
          header-align="center"
          label="验证方式"></el-table-column>
        <el-table-column width="160" label="修改/复核人员">
          <template slot-scope="scope">
            <div v-if="userMap[scope.row.nowData.updated_by_id]">
              修改人：{{ userMap[scope.row.nowData.updated_by_id] }}
            </div>
            <div v-if="userMap[scope.row.nowData.reviewer_id]">
              复核人：{{ userMap[scope.row.nowData.reviewer_id] }}
            </div>
          </template>
          <template> </template>
        </el-table-column>
        <el-table-column width="120" align="center">
          <template slot="header" slot-scope="{}">
            <el-popover
              v-model="popoverVisible"
              placement="bottom"
              width="160"
              popper-class="model-table-column-select-popper"
              trigger="hover">
              <div
                @click="handleReviewStatusAllClick"
                class="option-item"
                :class="{
                  'is-active':
                    filterParams.review_status.length === reviewStatusNums,
                }">
                <span>全部</span>
                <!-- <i class="el-icon-check"></i> -->
              </div>
              <div
                v-for="(value, key) in REVIEW_STATUS_FILTER_MAP"
                :key="value"
                class="option-item"
                :class="{
                  'is-active': filterParams.review_status === Number(key),
                }"
                @click="handleReviewStatusItemClick(Number(key))">
                <span>{{ value }}</span>
                <!-- <i class="el-icon-check"></i> -->
              </div>
              <span
                slot="reference"
                class="option-header"
                style="cursor: pointer">
                审核状态<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <div
              class="option-container"
              :class="{
                'not-pass': isNotPass(scope.row.nowData.review_status),
              }">
              <span>
                {{ REVIEW_STATUS_MAP[scope.row.nowData.review_status] }}
              </span>
              <el-tooltip
                v-if="
                  scope.row.nowData.meta &&
                  isNotPass(scope.row.nowData.review_status)
                "
                effect="dark"
                :content="scope.row.nowData.meta.review_reason"
                placement="top"
                popper-class="review-reason-popper">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="150" label="操作" align="center">
          <template slot-scope="scope">
            <div class="operation">
              <el-tooltip effect="dark" content="是否开启" placement="top">
                <el-switch
                  v-model="scope.row.enable"
                  :disabled="scope.row.abandoned"
                  @change="handleSwitchChange(scope.row)"
                  style="margin-right: 10px">
                </el-switch>
              </el-tooltip>
              <el-tooltip effect="dark" content="审核" placement="top">
                <el-button
                  v-if="canReview"
                  :type="!$platform.isDefaultEnv() ? 'primary' : 'text'"
                  size="small"
                  circle
                  :disabled="disabledReview(scope.row)"
                  @click.native.stop="handleReview(scope.row)">
                  <theme-icon
                    :name="disabledReview(scope.row) ? 'review-grey' : 'review'"
                    icon-class="el-icon-edit"></theme-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip
                v-if="!scope.row.abandoned && canEdit"
                effect="dark"
                content="修改审核规则"
                placement="top">
                <el-button
                  :type="!$platform.isDefaultEnv() ? 'primary' : 'text'"
                  size="small"
                  circle
                  @click.native.stop="editRule(scope.row)">
                  <theme-icon
                    name="edit-law"
                    icon-class="el-icon-edit"></theme-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip
                v-else
                effect="dark"
                content="查看规则"
                placement="top">
                <el-button
                  :type="!$platform.isDefaultEnv() ? 'primary' : 'text'"
                  size="small"
                  circle
                  @click.native.stop="lookRule(scope.row)">
                  <theme-icon
                    name="edit-law"
                    icon-class="el-icon-edit"></theme-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip effect="dark" content="删除规则" placement="top">
                <el-button
                  v-if="canEdit"
                  :type="!$platform.isDefaultEnv() ? 'danger' : 'text'"
                  size="small"
                  circle
                  :disbaled="disabledDelete(scope.row)"
                  @click.native.stop="deleteRule(scope.row)">
                  <theme-icon
                    :name="
                      disabledDelete(scope.row)
                        ? 'delete-grey-law'
                        : 'delete-law'
                    "
                    icon-class="el-icon-delete"></theme-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="total, prev, pager, next, sizes"
        :page-sizes="[10, 20, 50]"
        :current-page="searchForm.page"
        :page-size="searchForm.size"
        :total="pager.total"
        @current-change="handleChangePage"
        @size-change="handleChangeSize">
      </el-pagination>
      <edit-rule-dialog
        v-if="editRuleVisible"
        :mode="editRuleMode"
        :loading="editRuleLoading"
        :type-options="lawTypeOptions"
        :original-data="originalRuleData"
        :now-data="currentRuleData"
        @close="handleEditRuleClose"
        @submit="handleEditRuleSubmit"
        @approve="handleEditRuleApprove"
        @reject="handleEditRuleReject"
        @delete="handleEditRuleDelete"
        @cancleDel="handleEditRuleCancleDel"></edit-rule-dialog>
    </template>
    <full-test-model-rules
      v-if="isShowingFullTest"
      @goBack="handleGoBack"
      @detail="handleDetail"
      @relate="handleRelate"
      @reTest="handleReTest" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { laws as lawsApi } from '@/store/api';
import EditRuleDialog from '../components/EditRuleDialog.vue';
import FullTestModelRules from '../components/FullTestModelRules.vue';
import {
  REVIEW_STATUS,
  REVIEW_STATUS_MAP,
  REVIEW_STATUS_FILTER,
  EDIT_RULE_DIALOG_MODE,
  REVIEW_STATUS_FILTER_MAP,
} from '@/store/constants';
const ABANDONED_MAP = {
  NOT: 'not',
  YES: 'yes',
};
export default {
  name: 'custom-rules',
  components: {
    EditRuleDialog,
    FullTestModelRules,
  },
  data() {
    return {
      REVIEW_STATUS,
      REVIEW_STATUS_MAP,
      REVIEW_STATUS_FILTER,
      ABANDONED_MAP,
      EDIT_RULE_DIALOG_MODE,
      REVIEW_STATUS_FILTER_MAP,
      isLoading: false,
      searchForm: {
        field: 'law_name',
        keyword: '',
        abandoned: Object.values(ABANDONED_MAP),
        page: 1,
        size: 10,
      },
      filterParams: {
        review_status: '',
      },
      searchOptions: [
        { label: '法规名称', value: 'law_name', placeholder: '请输入法规名称' },
        {
          label: '法规内容',
          value: 'rule_content',
          placeholder: '请输入法规内容',
        },
        { label: '规则名称', value: 'name', placeholder: '请输入规则名称' },
      ],
      tableData: [],
      pager: {
        total: 0,
      },
      editRuleVisible: false,
      editRuleMode: EDIT_RULE_DIALOG_MODE.REVIEW,
      currentRuleData: null, // 当前操作的规则数据
      originalRuleData: null, // 原始规则数据（用于审核对比）
      lawTypeOptions: [],
      isShowingFullTest: false,
      userMap: {},
      popoverVisible: false,
      editRuleLoading: false,
    };
  },
  computed: {
    ...mapGetters(['loginUser']),
    canReview() {
      if (this.$features.supportRuleReview()) {
        return this.$isAllowed('customerRuleReview');
      }
      return false;
    },
    canEdit() {
      return this.$isAllowed('customerRuleParticipate');
    },
    currentSearchOption() {
      return this.searchOptions.find(
        (option) => option.value === this.searchForm.field,
      );
    },
    params() {
      const params = {
        page: this.searchForm.page,
        size: this.searchForm.size,
      };
      if (this.searchForm.keyword) {
        params[this.searchForm.field] = this.searchForm.keyword;
      }
      if (this.filterParams.review_status) {
        params.review_status = this.filterParams.review_status;
      }
      if (this.searchForm.abandoned.length === 1) {
        params.abandoned =
          this.searchForm.abandoned[0] === this.ABANDONED_MAP.YES
            ? true
            : false;
      }
      return params;
    },
    reviewStatusNums() {
      return Object.keys(REVIEW_STATUS_FILTER).length;
    },
  },
  methods: {
    handleClickPopover() {
      this.popoverVisible = !this.popoverVisible;
    },
    isNotPass(review_status) {
      return [REVIEW_STATUS.NOT_PASS, REVIEW_STATUS.DEL_NOT_PASS].includes(
        review_status,
      );
    },
    disabledReview(row) {
      return (
        row.nowData.updated_by_id === this.loginUser.id ||
        ![REVIEW_STATUS.NOT_REVIEWED, REVIEW_STATUS.DEL_NOT_REVIEWED].includes(
          row.nowData.review_status,
        )
      );
    },
    handleGoBack() {
      this.isShowingFullTest = false;
    },
    handleDetail(row) {
      console.log(row);
    },
    handleRelate(row) {
      console.log(row);
    },
    handleReTest(row) {
      console.log(row);
    },
    handleReviewStatusAllClick() {
      // if (this.filterParams.review_status.length < this.reviewStatusNums) {
      //   this.filterParams.review_status = Object.values(REVIEW_STATUS_FILTER);
      // } else {
      this.filterParams.review_status = [];
      // }
      this.handleSearchClick();
    },
    async handleReview(row) {
      try {
        // 设置为审核模式
        this.editRuleMode =
          row.review_status === REVIEW_STATUS.DEL_NOT_REVIEWED
            ? EDIT_RULE_DIALOG_MODE.DELETE_REVIEW
            : EDIT_RULE_DIALOG_MODE.REVIEW;
        this.currentRuleData = {
          ...row.nowData,
          scenario_names: row.scenario_names,
          ruleId: row.rule_id,
          id: row.id,
        };

        // 获取原始数据用于对比
        this.originalRuleData = row;
        this.editRuleVisible = true;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: '获取原始规则数据失败：' + error.message,
          type: 'error',
        });
      }
    },
    handleReviewStatusItemClick(val) {
      // const isExist = this.filterParams.review_status.includes(val);
      // if (isExist) {
      //   this.filterParams.review_status =
      //     this.filterParams.review_status.filter((item) => {
      //       return item !== val;
      //     });
      // } else {
      //   this.filterParams.review_status.push(val);
      // }
      this.popoverVisible = false;
      if (this.filterParams.review_status === val) {
        this.filterParams.review_status = null;
      } else {
        this.filterParams.review_status = val;
      }
      this.handleSearchClick();
    },
    formatTableData(data) {
      return data.map((item) => {
        const nowData = item.draft ? item.draft : item;
        const {
          check_method,
          check_type,
          core,
          id,
          name,
          review_status,
          rule_content,
          subject,
          updated_by_id,
          reviewer_id,
          meta,
        } = nowData;
        return {
          ...item,
          nowData: {
            check_method,
            check_type,
            core,
            id,
            name,
            review_status,
            rule_content,
            subject,
            updated_by_id,
            reviewer_id,
            meta,
          },
        };
      });
    },
    async getCheckPoints(options = { loading: true }) {
      try {
        if (options.loading) {
          this.isLoading = true;
        }
        const { items, total, user_map } = await lawsApi.getCheckPoints(
          this.params,
        );

        this.tableData = this.formatTableData(items);
        this.userMap = user_map;
        this.pager.total = total;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        if (options.loading) {
          this.isLoading = false;
        }
      }
    },
    handleOpenFullTest() {
      this.isShowingFullTest = true;
    },
    handleSearchFieldChange() {},
    handleSearchClick() {
      this.searchForm.page = 1;
      this.getCheckPoints();
    },
    handleChangeSize(size) {
      this.searchForm.page = 1;
      this.searchForm.size = size;
      this.getCheckPoints();
    },
    handleChangePage(page) {
      this.searchForm.page = page;
      this.getCheckPoints();
    },
    async handleSwitchChange(row) {
      try {
        await lawsApi.switchCheckPoints(row.id, {
          enable: row.enable,
        });
        this.$notify({
          title: '成功',
          message: row.enable ? '启用成功' : '禁用成功',
          type: 'success',
        });
      } catch (error) {
        row.enable = !row.enable;
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },
    disabledDelete() {},
    disabledEdit() {},
    deleteRule(row) {
      this.$confirm(
        `是否删除该规则？${row.abandoned ? '' : '（将进入复核）'}`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        },
      ).then(async () => {
        try {
          if (row.abandoned) {
            await lawsApi.deleteCheckPoint(row.id);
          } else {
            await lawsApi.startDeleteCheckPoint(row.id);
          }
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
          });
          this.handleEditRuleClose();
          this.getCheckPoints({ loading: false });
        } catch (error) {
          this.$notify({
            title: '错误',
            message: error.message,
            type: 'error',
          });
        }
      });
    },
    editRule(row) {
      // 设置为编辑模式
      this.editRuleMode = EDIT_RULE_DIALOG_MODE.EDIT;
      this.currentRuleData = {
        ...row.nowData,
        scenario_names: row.scenario_names,
        ruleId: row.rule_id,
        id: row.id,
      };
      this.originalRuleData = null; // 编辑模式不需要原始数据对比
      this.editRuleVisible = true;
    },
    lookRule(row) {
      this.editRuleMode = EDIT_RULE_DIALOG_MODE.VIEW;
      this.currentRuleData = {
        ...row.nowData,
        scenario_names: row.scenario_names,
        ruleId: row.rule_id,
        id: row.id,
      };
      this.originalRuleData = null;
      this.editRuleVisible = true;
    },
    handleEditRuleClose() {
      this.editRuleVisible = false;
      this.editRuleMode = EDIT_RULE_DIALOG_MODE.EDIT;
      this.currentRuleData = null;
      this.originalRuleData = null;
    },
    async handleEditRuleSubmit(data) {
      this.editRuleLoading = true;
      try {
        await lawsApi.updateCheckPoint(this.currentRuleData.id, data);
        this.$message.success('规则提交成功');
        this.handleEditRuleClose();
        this.getCheckPoints({ loading: false });
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        this.editRuleLoading = false;
      }
    },
    handleEditRuleApprove() {
      this.$confirm('确认审核通过该规则？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            this.editRuleLoading = true;
            await lawsApi.reviewCheckPoint(this.currentRuleData.id, {
              review_status: REVIEW_STATUS.PASS,
              review_reason: '',
            });
            this.$message.success('审核通过成功');
            this.handleEditRuleClose();
            this.getCheckPoints({ loading: false });
          } catch (error) {
            this.$notify({
              title: '错误',
              message: error.message,
              type: 'error',
            });
          } finally {
            this.editRuleLoading = false;
          }
        })
        .catch(() => {});
    },
    async handleEditRuleReject(reason) {
      try {
        this.editRuleLoading = true;
        await lawsApi.reviewCheckPoint(this.currentRuleData.id, {
          review_status: REVIEW_STATUS.NOT_PASS,
          review_reason: reason,
        });
        this.$notify({
          title: '成功',
          message: '提交成功',
          type: 'success',
        });
        this.handleEditRuleClose();
        this.getCheckPoints({ loading: false }); // 刷新列表
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        this.editRuleLoading = false;
      }
    },
    handleEditRuleDelete() {
      this.$confirm('确认删除该规则？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            this.editRuleLoading = true;
            await lawsApi.reviewCheckPoint(this.currentRuleData.id, {
              review_status: REVIEW_STATUS.PASS,
              review_reason: '',
            });
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
            });
            this.handleEditRuleClose();
            this.getCheckPoints({ loading: false });
          } catch (error) {
            this.$notify({
              title: '错误',
              message: error.message,
              type: 'error',
            });
          } finally {
            this.editRuleLoading = false;
          }
        })
        .catch(() => {});
    },
    handleEditRuleCancleDel() {
      this.$confirm('确认取消删除该规则？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            this.editRuleLoading = true;
            await lawsApi.reviewCheckPoint(this.currentRuleData.id, {
              review_status: REVIEW_STATUS.DEL_NOT_PASS,
              review_reason: '该条规则删除请求不通过',
            });
            this.$notify({
              title: '成功',
              message: '取消删除成功',
              type: 'success',
            });
            this.handleEditRuleClose();
            this.getCheckPoints({ loading: false });
          } catch (error) {
            this.$notify({
              title: '错误',
              message: error.message,
              type: 'error',
            });
          } finally {
            this.editRuleLoading = false;
          }
        })
        .catch(() => {});
    },
    async getScenarios() {
      try {
        this.lawTypeOptions = await lawsApi.getScenarios();
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      }
    },
  },
  created() {
    if (this.canReview) {
      this.getScenarios();
    }
    this.getCheckPoints();
  },
};
</script>
<style lang="scss" scoped>
.custom-rules {
  padding: 0 20px;
  .operater-list {
    display: flex;
    justify-content: space-between;
    align-items: center;

    ::v-deep .el-form {
      .el-form-item {
        margin-bottom: 0;
        margin-right: 0;
        &:last-child {
          margin-left: 10px;
        }
        .el-form-item__content {
          line-height: initial;
        }
        .search-field {
          width: 140px;
          margin-left: 20px;
          .el-input__inner {
            border-right: none;
            border-radius: 4px 0 0 4px;
          }
        }
        .search-input,
        .search-select {
          width: 300px;
          .el-input__inner {
            border-radius: 0 4px 4px 0;
          }
        }
        .search-btn {
          height: 36px;
          font-size: 16px;
          padding: 7px 20px;
          vertical-align: middle;
          > span {
            display: flex;
            align-items: center;
            .svg-font-icon {
              margin-right: 5px;
            }
          }
        }
      }
    }
    .operater-list-left {
      display: flex;
      align-items: center;
      .test-icon-wrapper {
        display: flex;
        align-items: center;
        column-gap: 8px;
      }
    }
  }
  ::v-deep .rule-list-table {
    .operation {
      display: flex;
      align-items: center;
    }
  }
  .el-pagination {
    padding: 20px 0;
    text-align: center;
  }
  .option-container {
    display: flex;
    align-items: center;
    column-gap: 8px;
    &.not-pass {
      color: #b14435;
    }
  }
}
</style>
<style lang="scss">
.el-popover.model-table-column-select-popper {
  padding: 10px 0;
  max-height: 388px;
  overflow-y: auto;
  .option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    padding: 0 20px;
    cursor: pointer;

    &:hover {
      background: #f3f7fc;
      color: $--color-primary;
    }

    .el-icon-check {
      visibility: hidden;
      color: $--color-primary;
    }

    &.is-active {
      background: #f3f7fc;
      color: $--color-primary;
      .el-icon-check {
        visibility: visible;
      }
    }
  }
}
</style>
<style lang="scss">
.review-reason-popper {
  &.el-tooltip__popper {
    max-width: 400px;
  }
}
</style>
