.citics-tg-page {
  padding: 25px 20px 0;
  min-width: 966px;
  overflow-y: scroll;

  .el-pagination span:not([class*='suffix']) {
    line-height: 31px;
    height: 31px;
  }
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;

    .el-breadcrumb {
      display: flex;
      align-items: center;

      .el-breadcrumb__item:first-child {
        span {
          color: #0090c0;
          text-decoration: underline;
          font-weight: 400;
        }
      }

      .project-name {
        .el-breadcrumb__inner {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .el-table {
    width: 100%;
    .el-table__row {
      cursor: pointer;
    }

    .el-table__expanded-cell {
      padding: 20px 50px;
    }

    .el-table__header {
      th.operations-column {
        padding: 0;
        .cell {
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding-right: 40px;
        }
      }
      .column-filter-ref {
        width: 14px;
        height: 14px;
        vertical-align: sub;
        margin-left: 10px;
        cursor: pointer;
        position: absolute;
        right: 0;
        top: 0;
        padding: 13px;
        box-shadow: -2px 0 5px 0 #0000001a;
      }
    }

    .name {
      .cell {
        display: flex;
        align-items: center;
        i {
          font-size: 16px;
        }
      }
    }
    .time {
      .cell {
        word-break: keep-all;
      }
    }

    .icontext {
      font-size: 14px;
    }

    .box-icon {
      margin-right: 10px;
      font-size: 18px;
      vertical-align: -1px;
    }

    .el-tag {
      margin-right: 5px;
    }
    td.para-texts-td {
      .el-tag {
        max-width: 160px !important;
      }
    }
    .ellipsis-column {
      .cell {
        white-space: nowrap;
      }
    }
    td.tags-column {
      .cell p {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 32px;
        .el-tag {
          max-width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .folder {
    width: 26px;
    margin-right: 10px;
  }

  .el-pagination {
    padding: 20px 0;
    text-align: center;
  }
}
.all-tags-tooltip {
  max-width: 500px;
  padding-top: 5px !important;
  padding-right: 5px !important;
  .el-tag {
    padding: 8px 5px;
    margin: 5px 5px 0 0;
    max-width: 100%;
    white-space: wrap;
    height: auto;
    line-height: normal;
    background-color: #e6f4f9;
    border-color: #cce9f2;
  }
  > div {
    max-height: 200px;
    overflow-y: auto;
  }
}
.mapping-check-wrapper {
  .el-collapse {
    border: none;
    .el-collapse-item {
      margin-bottom: 3px;
    }
    .collapse-item-error {
      ::v-deep .el-collapse-item__header {
        border: 1px solid red;
        color: red;
        i {
          color: red;
        }
      }
    }
    .el-collapse-item__header {
      border: 1px solid #e9f3f9;
      padding-left: 25px;
      position: relative;
      background: #e9f3f9;
      color: #0090c0;
      font-size: 16px;
      i {
        position: absolute;
        left: 4px;
      }
    }
  }

  .el-collapse-item__content {
    padding-bottom: 0;
  }
  .el-collapse-item__wrap {
    border: none;
  }
  .mapping-check-relation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px;
    padding: 0 31px 0 10px;
    &.item-error {
      padding-right: 10px;
      .item-box {
        border: 1px solid red !important;
        color: red !important;
      }
      .el-icon-right {
        color: red;
      }
    }
    img {
      width: 20px;
      height: auto;
      margin: 0 5px;
    }
    .item-box {
      padding: 2px 5px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      font-size: 14px;
      min-height: 25px;
    }
    .item-version {
      margin-left: 10px;
    }
    .mapping-to-value {
      .item-box + .item-box {
        margin-top: 5px;
      }
    }
    > div {
      flex: 1;
    }
    .el-icon-right {
      margin: 5px;
    }
    i {
      color: #0090c0;
      font-size: 16px;
      margin-left: 5px;
      cursor: pointer;
    }
  }
  .empty-info {
    text-align: center;
  }
  .not-find-error-tip {
    font-size: 12px;
    color: red;
    a {
      color: #0090c0;
      text-decoration: none;
    }
  }
}
.citics-tg-remark {
  .document-viewer-toolbar {
    .left {
      .el-button {
        margin: 0 10px !important;
        padding: 0 !important;
      }
    }
  }
}
