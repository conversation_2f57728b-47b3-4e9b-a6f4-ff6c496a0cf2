@import './theme.scss';

.citics-dcm-page {
  height: 100vh !important;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .project-list,
  .file-list,
  .schema-container,
  .user-container {
    padding: 25px 30px;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: auto;
    overflow-y: unset;

    .page-header {
      margin: 0px;

      > * {
        margin-bottom: 25px;
      }
    }

    .el-table {
      margin-bottom: 16px;
      overflow-x: auto;

      tr {
        td,
        th {
          text-align: left !important;

          &:first-child {
            text-align: center !important;
          }
        }
      }
    }

    .el-table__header-wrapper,
    .el-table__body-wrapper {
      overflow: unset;
    }

    .el-pagination {
      padding: 0px;
      text-align: center;
    }
  }

  .schema-container {
    .operations {
      .cell {
        display: flex;
        justify-content: flex-start;
      }
    }
  }

  .schema-tree {
    padding: 25px 30px;

    > .el-container {
      > .el-header {
        padding: 0px;
        margin: 0px;
        margin-bottom: 25px;
        height: auto !important;
      }

      > .el-main {
        padding: 0px;
      }
    }
  }

  .login-wrapper {
    .login-form {
      .login-button {
        .el-button {
          &.el-button--primary {
            background: $--color-primary;
            border-color: $--color-primary;
          }
        }
      }
    }
  }

  .viewer {
    div[data-id='loadingBar'] {
      .progress {
        background-color: $--color-primary;
      }
    }
  }

  .document-viewer-toolbar {
    .el-button {
      i {
        &:hover {
          color: $--color-primary;
        }
      }
    }
  }

  .draw-widget-switch-container {
    .draw-widget-switch {
      button {
        &.active {
          background-color: $--color-primary;
          border: 1px solid $--color-primary;

          &:before {
            background-color: $--color-primary;
            color: #fff;
          }
        }
      }
    }
  }
}
