<template>
  <i class="svg-font-icon" :class="classes" :style="styles"></i>
</template>

<script>
import { isNumber } from 'lodash-es';

export default {
  name: 'svg-font-icon',
  props: {
    name: {
      type: String,
      required: true,
    },
    size: {
      type: [String, Number],
      required: false,
      default: 'medium',
    },
    color: {
      type: String,
      required: false,
      default: 'inherit',
    },
  },
  computed: {
    classes() {
      if (!isNumber(this.size)) {
        const sizeClass = `svg-font-icon-${this.size}`;
        return {
          [sizeClass]: true,
          [`pd-icon-${this.name}`]: true,
        };
      }
      return {
        [`pd-icon-${this.name}`]: true,
      };
    },
    styles() {
      if (isNumber(this.size)) {
        return {
          fontSize: `${this.size}px`,
          color: this.color,
        };
      }
      return {
        color: this.color,
      };
    },
  },
};
</script>

<style scoped lang="scss">
.svg-font-icon {
  font-style: normal;
  vertical-align: middle;
  font-family: 'pd-icon';
  cursor: pointer;

  &.svg-font-icon-large {
    font-size: 16px;
  }

  &.svg-font-icon-medium {
    font-size: 14px;
  }

  &.svg-font-icon-small {
    font-size: 12px;
  }
}
</style>
