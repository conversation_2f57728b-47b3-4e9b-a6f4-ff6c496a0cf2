<template>
  <el-aside
    width="40%"
    class="file-remark-aside"
    v-show="showAnswerPanel"
    v-loading="loading">
    <file-switch-btn v-if="showFileSwitchBtn" :qid="qid"></file-switch-btn>

    <p class="ai-predict-failed-tip" v-if="isAiPredictFailed">
      预测失败，请重新识别
    </p>
    <div class="file-aside-tabs">
      <div v-if="isShowRemarkTabs" v-show="isShowRemarkType">
        <el-tabs
          :class="{ 'ai-predict-failed': isAiPredictFailed }"
          v-model="activeName"
          :stretch="true"
          @tab-click="handleTabClick(activeName)">
          <el-tab-pane label="要素提取" name="remark">
            <file-remark-operate
              ref="fileRemarkOperate"
              v-if="Object.keys(question).length > 0"
              :qid="qid"
              :answerVersion="answerVersion"
              :answerItemMap="answerItemMap"
              :updatedAnswerKeys="updatedAnswerKeys"
              :is-inspect="true"
              :task-type="taskType"
              @before-send-question-answer="beforeSendQuestionAnswer"
              @reload-question="handleReloadQuestion"
              @submit-inspect-answer="
                $emit('submit-inspect-answer')
              "></file-remark-operate>

            <remark-validate-result
              v-if="errorTips.length > 0"></remark-validate-result>

            <div v-if="showAnswerDataSelect" class="answer-data-select">
              <span class="label">场景：</span>
              <el-select
                v-model="currentAnswerMoldId"
                size="mini"
                placeholder="请选择场景"
                @change="setAnswerData">
                <el-option
                  v-for="(item, index) in question"
                  :key="index"
                  :label="item.mold.name"
                  :value="item.mold.id">
                </el-option>
              </el-select>
            </div>

            <div
              class="search-and-collpase-answer"
              v-if="Object.keys(this.currentSchema).length > 0">
              <file-remark-tree-nodes-searcher></file-remark-tree-nodes-searcher>

              <toggle-answer-collapse-state
                @toggle="toggleAllAnswerCollapseState">
              </toggle-answer-collapse-state>
            </div>
            <file-remark-answer-panel
              :qid="qid"
              :schema-id="schemaId"
              :read-only="readOnly"
              :answerItemMap="answerItemMap"
              ref="remarkAnswer"></file-remark-answer-panel>
          </el-tab-pane>
          <el-tab-pane
            v-if="isShowRuleAuditTab"
            label="规则审核"
            name="ruleAudit"
            :disabled="ruleAuditTabDisabled">
            <rule-audit
              ref="ruleAudit"
              :qid="qid"
              :file-id="fileId"
              :schema-id="schemaId"
              :current-answer-mold-id="currentAnswerMoldId">
            </rule-audit>
          </el-tab-pane>
          <el-tab-pane v-if="isShowCheckTab" label="要素核查" name="check">
            <nafmii-consistency-comparison
              v-if="isShowNafmiiConsistencyComparison"
              ref="check"
              :data="answersData.diff" />
          </el-tab-pane>
        </el-tabs>
      </div>
      <nafmii-identify-aside
        v-if="isShowIdentifyTabs"
        :file-id="fileId"
        :answers-data="answersData"
        :is-show-remark-type="isShowRemarkType"
        :viewer-ready="viewerReady"
        @answers-updated-success="getAnswers" />
    </div>
  </el-aside>
</template>

<script>
import { mapGetters } from 'vuex';
import EventBus from '../components/remark/remark-tree/EventBus';
import FileSwitchBtn from './FileSwitchBtn.vue';
import FileRemarkOperate from '@/components/remark/FileRemarkOperate';
import RemarkValidateResult from '@/components/remark/FileRemarkValidateResult';
import FileRemarkTreeNodesSearcher from '@/components/remark/FileRemarkTreeNodesSearcher';
import FileRemarkAnswerPanel from '@/components/remark/FileRemarkAnswerPanel';
import RuleAudit from '@/components/RuleAudit';
import NafmiiConsistencyComparison from '@/custom/nafmii/components/ConsistencyComparison';
import NafmiiIdentifyAside from '@/custom/nafmii/components/IdentifyAside';
import ToggleAnswerCollapseState from './ToggleAnswerCollapseState.vue';
import { parseQueryFromBase64EncodedUrl } from '@/utils';
import { isRemarkType } from '../custom/nafmii/common/utils';
import { nafmii as nafmiiApi } from '../store/api';

import FileMarkableMixin from '@/components/mixins/FileMarkableMixin';
import { AI_PREDICT_STATUS_MAP } from '../store/constants';
import _ from 'lodash';

export default {
  name: 'file-remark-aside',
  mixins: [FileMarkableMixin],
  components: {
    FileSwitchBtn,
    FileRemarkOperate,
    RemarkValidateResult,
    FileRemarkTreeNodesSearcher,
    FileRemarkAnswerPanel,
    RuleAudit,
    ToggleAnswerCollapseState,
    NafmiiConsistencyComparison,
    NafmiiIdentifyAside,
  },
  props: {
    qid: {
      type: Number,
      required: true,
    },
    fileId: {
      type: Number,
      required: true,
    },
    schemaId: {
      type: Number,
      required: true,
    },
    answerVersion: {
      type: String,
      required: true,
    },
    answerItemMap: {
      type: Object,
      required: true,
    },
    updatedAnswerKeys: {
      type: Set,
      required: true,
    },
    reloadQuestion: {
      type: Function,
      required: true,
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
    showAnswerPanel: {
      type: Boolean,
      default: true,
    },
    viewerReady: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters('remarkModule', [
      'errorTips',
      'question',
      'currentSchema',
      'diffEnabled',
      'remarkTaskTypes',
    ]),
    ...mapGetters('detailModule', ['fileViewer']),
    showFileSwitchBtn() {
      return (
        this.$route.name !== 'inspectBase64Encoded' &&
        !this.$route.query.notProjectFile
      );
    },
    showCommonOperateBtns() {
      return !this.$route.meta.noAuth;
    },
    taskType() {
      let taskType = this.$route.query.task_type;

      if (this.$route.name === 'inspectBase64Encoded') {
        const query = parseQueryFromBase64EncodedUrl(
          this.$route.params.base64EncodedString,
        );
        taskType = query.task_type;
      }

      return taskType || 'extract';
    },
    isShowRuleAuditTab() {
      if (this.$platform.isNafmiiEnv()) {
        return false;
      }
      return this.taskType === 'audit';
    },
    isShowCheckTab() {
      return this.$platform.isNafmiiEnv() && !this.isAiPredictFailed;
    },
    isShowRemarkType() {
      if (this.$platform.isNafmiiEnv()) {
        return isRemarkType(this.$route.query.type);
      }
      return true;
    },
    isShowRemarkTabs() {
      if (this.$platform.isNafmiiEnv()) {
        return this.remarkTaskTypes.includes('remark');
      }
      return true;
    },
    isShowIdentifyTabs() {
      if (this.$platform.isNafmiiEnv()) {
        return (
          this.remarkTaskTypes.includes('keywords') ||
          this.remarkTaskTypes.includes('sensitiveWords')
        );
      }
      return false;
    },
    isShowNafmiiConsistencyComparison() {
      return this.$platform.isNafmiiEnv() && this.answersData?.diff;
    },
    isAiPredictFailed() {
      if (!this.$platform.isNafmiiEnv()) {
        return false;
      }
      const currentFile = this.fileViewer?.files.find(
        (file) => file.id === this.fileId,
      );
      const questions = currentFile?.questions || [];
      const questionGroups = _.groupBy(questions, 'ai_status');
      const isFailed = Object.keys(questionGroups).some(
        (key) => Number(key) === AI_PREDICT_STATUS_MAP.FAILED,
      );
      return isFailed;
    },
    showAnswerDataSelect() {
      return this.$platform.isCmfchinaEnv() && this.question?.length > 1;
    },
  },
  data() {
    return {
      activeName: 'remark',
      ruleAuditTabDisabled: false,
      answersData: {},
      loading: false,
      currentAnswerMoldId: Number(this.$route.query.schemaId) || null,
    };
  },
  created() {
    if (this.$platform.isNafmiiEnv()) {
      this.getAnswers();
    }
  },
  watch: {
    taskType: {
      handler() {
        this.getActiveName();
      },
      immediate: true,
    },
    '$route.query.type'() {
      if (this.$platform.isNafmiiEnv()) {
        this.getActiveName();
      }
    },
    fileId() {
      if (this.$platform.isNafmiiEnv()) {
        this.getAnswers();
      }
      this.currentAnswerMoldId = null;
    },
  },
  methods: {
    setAnswerData() {
      const answerDataList = this.question || [];
      const answer =
        answerDataList.find(
          (item) => item.mold.id === this.currentAnswerMoldId,
        ) || {};
      this.$store.commit('remarkModule/SET_ANSWER_SCHEMA', {});
      this.$store.commit('remarkModule/SET_USER_ANSWER', {});
      this.$nextTick(() => {
        this.$store.commit('remarkModule/SET_ANSWER_SCHEMA', answer.mold?.data);
        this.$store.commit('remarkModule/SET_USER_ANSWER', {
          items: answer.answer_data,
        });
        this.$store.dispatch('remarkModule/initAnswerTree');
      });
      this.$emit('change-answer-data');
      EventBus.$emit('toggle-submit-answer-disabled', true);
    },
    getActiveName() {
      if (!this.isShowRuleAuditTab) {
        this.activeName = 'remark';
      }
      if (this.isShowRemarkType) {
        this.$store.commit('remarkModule/SET_REMARK_TAB', this.activeName);
      }
    },
    toggleAllAnswerCollapseState() {
      this.$refs.remarkAnswer.toggleAllAnswerCollapseState();
    },
    handleTabClick(name) {
      if (name === 'ruleAudit') {
        this.$refs.ruleAudit.fetchFileAuditRuleResult();
      } else if (name === 'remark') {
        if (this.$platform.isCmfchinaEnv()) {
          this.handleReloadQuestion();
        }
      }
      this.$store.commit('remarkModule/SET_REMARK_TAB', name);
    },
    beforeSendQuestionAnswer() {
      this.ruleAuditTabDisabled = true;
    },
    handleReloadQuestion() {
      if (this.$route.query.task_type === 'audit') {
        this.ruleAuditTabDisabled = false;
      }
      this.reloadQuestion();
    },
    async getAnswers() {
      try {
        this.loading = true;
        let res = await nafmiiApi.fetchAnswers(this.fileId, true);
        this.answersData = res.data;
      } catch (error) {
        this.$notify({
          title: '错误',
          message: error.message,
          type: 'error',
        });
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.file-remark-aside {
  display: flex;
  flex-flow: column;
  height: 100%;
  overflow: hidden;
}

::v-deep .navigate-btns {
  height: 50px;
  box-sizing: border-box;
  padding: 10px 15px;
}

.file-aside-tabs {
  flex: 1;
  overflow: hidden;
  > div {
    display: flex;
    height: 100%;
  }
  .ai-predict-failed {
    ::v-deep .el-tabs__header {
      display: none;
    }
    .tag-user-wrapper,
    .search-and-collpase-answer {
      display: none;
    }
  }
  .answer-data-select {
    display: flex;
    align-items: center;
    margin: 0 15px 20px;
    .label {
      width: 50px;
      margin-right: 10px;
      font-size: 14px;
    }
    .el-select {
      width: 100%;
      box-sizing: border-box;
    }
  }
}

.ai-predict-failed-tip {
  color: #ff0000;
  padding: 10px;
  font-size: 14px;
  text-align: center;
}

.el-tabs {
  flex: 1;
  overflow: hidden;
}

::v-deep .el-tabs__header {
  position: sticky;
  top: 0;
  z-index: 4;
  background: #fff;
  margin-bottom: 0;

  .el-tabs__item {
    height: 42px;
    line-height: 42px;
    padding: 0;
  }
}

::v-deep .el-tabs__content {
  height: calc(100% - 42px);
  overflow: hidden;
}

::v-deep .el-tab-pane {
  display: flex;
  flex-flow: column;
  height: 100%;
  overflow: hidden;
}

.search-and-collpase-answer {
  display: flex;
  width: 100%;
  margin-bottom: 10px;
  padding-left: 15px;
  box-sizing: border-box;

  > div:first-of-type {
    flex: 1;
  }

  i {
    width: 40px;
    height: 28px;
    line-height: 28px;
    text-align: center;

    &::before {
      font-size: 18px;
    }
  }
}
</style>
