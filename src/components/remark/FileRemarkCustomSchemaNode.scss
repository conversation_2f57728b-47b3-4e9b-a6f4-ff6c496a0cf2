.remark-custom-schema-node {
  display: flex;
  flex-flow: column;
  padding: 10px;
  width: calc(100% - 20px);
  height: 100%;
  background: #f6f6f6;

  .schema-node-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    span,
    i {
      font-size: 16px;
    }
    .el-icon-close {
      cursor: pointer;
      &:hover {
        color: #777;
      }
    }
  }

  .schema-node-edit {
    flex: 1;
    overflow: auto;
    padding: 10px 0 20px 0;

    .el-input {
      width: 50%;
      margin: 10px 0 10px 8px;
    }
    .el-button {
      margin-left: 8px;
    }
    .validate-message {
      margin-left: 10px;
      font-size: 12px;
      color: #f56c6c;
    }
  }

  .schema-node {
    list-style: none;
  }

  .schema-node-label {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 4px;
    padding: 4px 8px;

    &.active {
      background: #bbdcfd;
    }

    .el-tag {
      height: 18px;
      line-height: 18px;
      margin-left: 10px;
      background-color: #ff9e02;
      border: none;
      color: #fff;
    }

    &.sub {
      label {
        color: grey;
      }
    }
  }

  .operation {
    display: flex;
    justify-content: flex-end;
    margin: 16px;
  }
}
