.remark-table-container {
  padding: 10px;
  width: calc(100% - 20px);
  height: 100%;
  background: #f6f6f6;

  .remark-table-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    span,
    i {
      font-size: 16px;
    }
    .el-icon-close {
      cursor: pointer;
      &:hover {
        color: #777;
      }
    }
  }

  .schema-tree {
    height: calc(100% - 100px);
    overflow: auto;
  }

  .schema-node {
    list-style: none;

    &:not([data-level='1']) {
      margin-left: 20px;
    }

    li {
      &.has-answer {
        label {
          color: #409eff;
          font-weight: bold;
        }
      }
    }

    .schema-node-label {
      display: flex;
      flex-flow: row;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      padding: 4px 8px;

      &.active {
        background: #bbdcfd;
      }

      label {
        flex: 1;
      }

      &.sub {
        label {
          color: grey;
        }
      }
    }

    .schema-node-answer {
      display: flex;
      align-items: center;
      margin-left: 20px;
      margin-bottom: 12px;
      width: calc(100% - 20px);

      p {
        flex: 1;
        padding: 4px 8px;
        border: 1px solid #a2cffc;
        border-radius: 4px;
        background: #fff;

        &.active {
          background: #bbdcfd;
        }
      }

      .far {
        padding: 8px;
        font-size: 16px;
        color: #f56c6c;
        cursor: pointer;
      }
    }
  }

  .schema-enum-answer {
    margin-left: 20px;
    margin-bottom: 10px;
  }

  .common-operation-button {
    padding: 2px 5px;
    font-size: 12px;
    margin: 0 5px;
  }

  .remark-table-operation {
    display: flex;
    justify-content: flex-end;
    margin: 16px;
  }
}
