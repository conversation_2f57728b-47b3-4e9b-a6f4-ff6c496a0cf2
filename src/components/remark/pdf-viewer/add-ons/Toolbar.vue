<template>
  <div class="document-viewer-toolbar">
    <div class="left header-left" v-if="showToolbarLeft">
      <template v-if="showGoBackButton">
        <el-button
          type="text"
          class="toolbar-back"
          :disabled="gobackBtnDisabled"
          @click="goBack">
          <theme-icon name="back" icon-class="fas fa-arrow-left"></theme-icon>
        </el-button>
      </template>

      <template v-if="showThumbButton">
        <el-button type="text" class="toolbar-thumb" @click="toggleThumbnail">
          <theme-icon name="thumb" icon-class="fas fa-images"></theme-icon>
        </el-button>
      </template>

      <template v-if="showChapterButton">
        <el-button type="text" class="toolbar-chapter" @click="toggleChapter">
          <theme-icon name="chapter" icon-class="fas fa-bars"></theme-icon>
        </el-button>
      </template>

      <search-bar
        v-if="showSearchButton"
        :search-result="searchResult"
        @search="search"
        @search-nav-clicked="searchNavClicked"></search-bar>

      <file-info v-if="showFileInfo" :file-info="fileInfo"></file-info>

      <slot name="toolbar"></slot>
    </div>
    <div class="middle">
      <div class="page-nav">
        <el-button
          type="text"
          icon="el-icon-caret-left"
          class="page-prev"
          :disabled="currentPage <= 1"
          @click="pageNavClicked('prev')"></el-button>
        <el-input
          v-model.number="currentPage"
          size="mini"
          @keydown.enter.native="changePageNumber" />
        <span>/</span>
        <span>{{ pageTotal }}</span>
        <el-button
          type="text"
          icon="el-icon-caret-right"
          class="page-next"
          :disabled="currentPage >= pageTotal"
          @click="pageNavClicked('next')"></el-button>
      </div>
    </div>
    <div class="right">
      <scale-select
        ref="scaleSelect"
        :default-scale="defaultScale"
        :get-scale="getScale"
        @scale-change="changeScale"></scale-select>
    </div>
  </div>
</template>

<script>
import SearchBar from './SearchBar';
import FileInfo from './FileInfo';
import ScaleSelect from './ScaleSelect';

export default {
  name: 'document-viewer-toolbar',
  components: { SearchBar, FileInfo, ScaleSelect },
  props: {
    showToolbarLeft: {
      type: Boolean,
      required: false,
      default: true,
    },
    showGoBackButton: {
      type: Boolean,
      required: false,
      default: true,
    },
    showThumbButton: {
      type: Boolean,
      required: false,
      default: true,
    },
    showChapterButton: {
      type: Boolean,
      required: false,
      default: true,
    },
    showSearchButton: {
      type: Boolean,
      default: true,
    },
    searchResult: {
      type: Object,
      required: true,
    },
    showFileInfo: {
      type: Boolean,
      required: false,
      default: true,
    },
    fileInfo: {
      type: Object,
      required: true,
    },
    pageTotal: {
      type: Number,
      required: true,
    },
    currentPageNumber: {
      type: Number,
      required: true,
    },
    defaultScale: {
      type: Number,
      default: -1,
    },
    getScale: {
      type: Function,
      required: true,
    },
  },
  computed: {
    currentPage: {
      get() {
        return this.currentPageNumber;
      },
      set(page) {
        this.$parent.currentPageNumber = page;
      },
    },
    gobackBtnDisabled() {
      return window.history.length <= 1;
    },
  },
  methods: {
    goBack() {
      this.$router.back();
    },
    toggleThumbnail() {
      this.$emit('toggle-thumbnail');
    },
    toggleChapter() {
      this.$emit('toggle-chapter');
    },
    search(searchConditions) {
      this.$emit('search', searchConditions);
    },
    searchNavClicked(index) {
      this.$emit('search-nav-clicked', index);
    },
    changePageNumber(page) {
      this.$emit('page-change', page);
    },
    pageNavClicked(direction) {
      this.$emit('page-nav-clicked', direction);
    },
    changeScale(scale) {
      this.$emit('scale-change', scale);
    },
    setScaleSelectValue(scale) {
      return this.$refs.scaleSelect.setScale(scale);
    },
  },
};
</script>

<style lang="scss" scoped>
.document-viewer-toolbar {
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  padding: 0 15px;
  background: #ffffff;
  border: 1px solid #eaedf3;
  box-sizing: border-box;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.2);

  ::v-deep .el-button {
    &.is-disabled {
      opacity: 0.3;
    }
    i {
      text-align: center;
      font-size: 18px;
      color: #333;
      cursor: pointer;
      &:hover {
        color: #409eff;
      }
    }
  }

  .left {
    display: flex;
    align-items: center;
    ::v-deep .el-button {
      margin: 0;
      padding: 8px;
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }

  .middle {
    display: flex;
    flex-flow: row;
    align-items: center;
    color: #3e3f42;

    .page-nav {
      display: flex;
      align-items: center;
      color: #3e3f42;
      & > * + * {
        margin-left: 5px;
      }
      .page-prev,
      .page-next {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        padding: 0;
        cursor: pointer;
        font-size: 20px;
        &[disabled] {
          ::v-deep i {
            cursor: not-allowed;
            color: #ccc;
          }
        }
      }

      .el-input {
        width: 50px;

        ::v-deep input {
          padding: 0 5px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          font-size: 14px;
        }
      }
    }
  }
  .right {
    ::v-deep .el-button {
      margin: 0;
    }
  }
}
</style>
