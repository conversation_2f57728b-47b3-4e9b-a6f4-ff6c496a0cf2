<template>
  <pdf-viewer
    v-if="pdfDocumentData"
    ref="pdfViewer"
    v-loading="fetchBoxTextLoading"
    :key="fileId"
    :event-bus="eventBus"
    :pdfjs-options="pdfjsOptions"
    :document-data="pdfDocumentData"
    :default-annotation-mode="defaultAnnotationMode"
    :annotation-options="annotationOptions"
    :show-annotation-tools="true"
    :tool-bar-items="[
      'back',
      'toggle',
      'find',
      'pageNum',
      'pageNavigator',
      'zoom',
    ]"
    resource-dir="static/next-pdf"
    @go-back="goback"
    @wireframe-draw-end="wireframeDrawEnd"
    @view-page-change="onPageChange"
    @view-page-rendered="onPageRendered"
    @change-annotation-mode="onChangeAnnotationMode"
    @after-table-cell-mousedown="afterTableCellMousedown"
    @annotation-clicked="onAnnotationClicked">
  </pdf-viewer>
</template>

<script>
import _ from 'lodash';
import Vue from 'vue';
import EventBus from '../remark-tree/EventBus';
import { mixDeepInfo, normalizeArrayJSON } from '../../../utils';
import { formatTablesToBoxes } from '@/utils/remarkAnswerTools';
import { baseURL } from '../../../store/api/http';
import { fetchOutlines } from '../../../store/api/detail.api.js';
import PDFViewer from 'vue-pdf-viewer-next/src/PDFViewer';
import ViewerMixin from './Viewer.mixin';

const chrono = require('chrono-node');

export default {
  name: 'pdfjs-viewer',
  components: {
    [PDFViewer.name]: PDFViewer,
  },
  mixins: [ViewerMixin],
  props: {
    pdfjsOptions: {
      type: Object,
      required: false,
      default: () => ({
        disableAutoSetTitle: true,
        locale: process.env.VUE_APP_LANG === 'EN' ? 'en-US' : 'zh-CN',
      }),
    },
    fileId: {
      type: Number,
      required: true,
    },
    answerItemMap: {
      type: Object,
      required: true,
    },
    fetchFileFn: {
      type: Function,
      required: false,
      default: (id) => `${baseURL}/plugins/fileapi/file/${id}/pdf`,
    },
  },
  data() {
    return {
      pdfDocumentData: null,
      eventBus: new Vue(),
      defaultAnnotationMode: 'wireframe',
      boxesData: [],
      annotationOptions: {
        wireframe: {
          style: {
            border: '2px dashed rgba(0, 0, 0, 0.7)',
          },
        },
      },
      currentPageTableBoxes: [],
      fetchBoxTextLoading: false,
    };
  },
  watch: {
    async fileId() {
      this.pdfDocumentData = null;
      await this.$nextTick();

      this.getPdfDocumentData();
    },
    answerItemMap() {
      if (this.nodeSelected) {
        const answer = this.answerItemMap[this.nodeSelected.schemaNodeKey];

        if (answer) {
          this.nodeSelected.answer = answer;
        } else {
          delete this.nodeSelected.answer;
          this.removeWidgetsBySchemaNode(this.nodeSelected.schemaNodeKey);
        }
      }

      if (this.nodeAnswerSelected) {
        const answer =
          this.answerItemMap[this.nodeAnswerSelected.schemaNodeKey];
        if (answer) {
          const isNodeAnswerSelectedBeRemoved = !answer.data.some(
            (answerItemData) =>
              _.isEqual(answerItemData, this.nodeAnswerSelected.data),
          );
          if (isNodeAnswerSelectedBeRemoved) {
            this.removeWidgetsBySchemaNode(
              this.nodeAnswerSelected.schemaNodeKey,
            );
            this.nodeAnswerSelected = null;
          }
        } else {
          this.removeWidgetsBySchemaNode(this.nodeAnswerSelected.schemaNodeKey);
          this.nodeAnswerSelected = null;
        }
      }
    },
  },
  created() {
    this.getPdfDocumentData();
  },
  methods: {
    async getPdfDocumentData() {
      this.pdfDocumentData = await this.fetchFileFn(this.fileId);
    },
    cancelSelectAllFrames() {
      if (this.isSelectedAll) {
        EventBus.$emit('cancel-select-frames');
      }
    },
    selectAnswerItem({ schemaNode, schema, data }) {
      this.nodeAnswerSelected = { schemaNodeKey: schemaNode, data };
      this.boxesData = [];
      this.clearAnswerBoxes();
      this.cancelSelectAllFrames();
      if (data.boxes.length === 0) {
        return;
      }

      const boxData = {
        boxes: data.boxes,
        tags: data.tags || [schema.data.label],
        handleType: 'wireframe',
      };
      this.boxesData.push(boxData);
      this.renderNodeAnnotations(this.boxesData);
      this.jumpFramePage(data.boxes[0].page);
      this.scrollToAnnotation(this.$refs.pdfViewer.$el);
    },
    selectSchemaNode({ model, ignoreAnswer }) {
      this.clearAnswerBoxes();

      const node = _.cloneDeep(model);
      const key = JSON.stringify(mixDeepInfo(node.meta));

      if (ignoreAnswer) {
        delete node.answer;
      } else {
        const answer = this.answerItemMap[key];
        if (answer) {
          node.answer = answer;
        } else {
          delete node.answer;
        }
      }

      this.nodeSelected = { ...node, schemaNodeKey: key };
    },
    selectRuleItem({ ruleItem, index = 0 }) {
      if (!ruleItem.schema_cols[0]) {
        this.eventBus.$emit('render-page-boxes', []);
        return;
      }
      let key = '';
      let boxDataIndex = 0;
      if (ruleItem.schema_cols.length > 1) {
        key = normalizeArrayJSON(ruleItem.schema_cols[index]);
      } else {
        key = normalizeArrayJSON(ruleItem.schema_cols[0]);
        boxDataIndex = index;
      }
      const answerItem = this.answerItemMap[key];
      const answerItemData = answerItem.data[boxDataIndex];
      if (!answerItemData) {
        this.eventBus.$emit('render-page-boxes', []);
        return;
      }
      const page = answerItemData.boxes[0].page;
      const annotations = [
        {
          ...answerItemData,
          tags: [answerItem.schema.data.label],
        },
      ];
      this.renderNodeAnnotations(annotations);
      this.jumpFramePage(page);
      this.scrollToAnnotation(this.$refs.pdfViewer.$el);
    },
    jumpFramePage(page) {
      this.eventBus.$emit('change-pdf-viewer-page', page + 1);
    },
    scrollToAnnotation(el) {
      const annotationElement = el.querySelector(
        '.annotation-wrap .annotation-box',
      );
      if (annotationElement) {
        annotationElement.scrollIntoView({
          block: 'center',
        });
      }
    },
    showPredictPosition(frame) {
      const page = frame.boxes[0].page;
      this.clearAnswerBoxes();
      this.eventBus.$emit('render-page-boxes', [frame]);
      this.jumpFramePage(page);
    },
    clearAnswerBoxes() {
      this.eventBus.$emit('render-page-boxes', [...this.currentPageTableBoxes]);
      this.isSelectedAll = false;
    },
    canDrawFrame() {
      if (_.isEmpty(this.nodeSelected)) {
        this.$notify({
          title: this.$t(`message['提示']`),
          message: this.$t(`message['尚未选择Schema节点，请勿标注']`),
          type: 'warning',
        });
        return false;
      } else if (this.nodeSelected.children.length > 0) {
        this.$notify({
          title: this.$t(`message['提示']`),
          message: this.$t(`message['当前schema为组合类型，请勿直接标注']`),
          type: 'warning',
        });
        return false;
      } else if (
        this.nodeSelected.answer &&
        this.nodeSelected.answer.data.length > 0 &&
        !this.nodeSelected.answer.schema.data.multi
      ) {
        // schema节点是否可标注多项
        this.$notify({
          title: this.$t(`message['提示']`),
          message: this.$t(`message['该字段不可标注多项，请勿标注']`),
          type: 'warning',
        });
        return false;
      }
      return true;
    },
    validateAnswerItem(answer) {
      const text = answer.boxes
        .map((box) => {
          return box.text;
        })
        .join('');
      const schemaType = this.nodeSelected.data.type;
      // 校验标注文本是否符合日期和数字格式
      if (schemaType === '日期') {
        const parsedDate = chrono.strict.parseDate(text);
        if (parsedDate === null) {
          this.$notify({
            title: this.$t(`message['提示']`),
            message: this.$t('message["“{text}” 日期格式错误"]', { text }),
            type: 'warning',
          });
        }
      }
      if (schemaType === '数字') {
        if (text.length === 0 || Number.isNaN(Number(text))) {
          this.$notify({
            title: this.$t(`message['提示']`),
            message: this.$t('message["“{text}” 数字格式错误"]', { text }),
            type: 'warning',
          });
        }
      }
    },
    async createAnswerItem(schemaNode, boxes) {
      EventBus.$emit('create-answer-item', {
        schemaNode,
        boxes,
        nodeSelected: this.nodeSelected,
      });
    },
    async wireframeDrawEnd(boxData) {
      if (!this.canDrawFrame()) {
        this.eventBus.$emit('destroy-rendered-temp-annotation');
        return;
      }

      try {
        this.fetchBoxTextLoading = true;
        this.boxesData = [];
        const boxParams = [];
        boxData.forEach((item) => {
          const box = {
            box_left: item.box.box_left,
            box_top: item.box.box_top,
            box_right: item.box.box_right,
            box_bottom: item.box.box_bottom,
          };
          const params = {
            box: Object.values(box),
            page: item.page,
          };
          boxParams.push(params);
        });

        const respBox = await this.fetchBoxTextContent(boxParams);
        const boxes = respBox.data.map((item, index) => {
          const box = item.box.box;
          const boxInfo = {
            box_left: box[0],
            box_top: box[1],
            box_right: box[2],
            box_bottom: box[3],
          };
          return {
            box: boxInfo,
            page: item.box.page,
            text: respBox.data[index].text,
          };
        });
        const tags = this.$features.showAnswerTypeOnAnnotation()
          ? ['Manual', this.nodeSelected.data.label]
          : [this.nodeSelected.data.label];
        const box = {
          boxes: boxes,
          tags: tags,
          type: 'wireframe',
        };
        this.boxesData.push(box);
        this.eventBus.$emit('destroy-rendered-temp-annotation');
        this.$refs.pdfViewer.setAnnotations([
          ...this.currentPageTableBoxes,
          ...this.boxesData,
        ]);
        this.validateAnswerItem(box);

        const schemaNode = JSON.stringify(mixDeepInfo(this.nodeSelected.meta));
        this.createAnswerItem(schemaNode, boxes);
      } catch (error) {
        this.$notify({
          title: this.$t(`message['错误']`),
          message: error.message,
          type: 'error',
        });
      } finally {
        this.fetchBoxTextLoading = false;
      }
    },
    renderNodeAnnotations(annotations) {
      const boxesData = [];
      const boxes = _.cloneDeep(annotations);
      boxes.forEach((box) => {
        const boxData = {
          boxes: box.boxes,
          tags: box.tags,
          type: box.handleType,
        };
        boxesData.push(boxData);
      });
      this.eventBus.$emit('render-page-boxes', boxesData);
      this.setAnnotationStyle();
    },
    setAnnotationStyle() {
      const annotations = this.$el.querySelectorAll('.annotation-box');
      annotations.forEach((item) => {
        item.classList.add('draw-box');
      });
    },
    fetchBoxTextContent(data) {
      return this.$store.dispatch('remarkModule/fetchBoxText', {
        fileId: this.fileId,
        data,
      });
    },
    renderAllAnnotations() {
      const boxesData = [];
      this.isSelectedAll = true;
      Object.values(this.answerItemMap).forEach((answer) => {
        if (answer.data.length > 0) {
          answer.data.forEach((ans) => {
            if (ans.boxes.length > 0) {
              const ansObj = {
                boxes: ans.boxes,
                tags: [answer.schema.data.label],
                type: ans.handleType,
              };
              boxesData.push(ansObj);
            }
          });
        }
      });
      if (boxesData.length === 0) {
        return;
      }
      const firstAnswerPage = boxesData[0].boxes[0].page;
      this.eventBus.$emit('render-page-boxes', boxesData);
      this.jumpFramePage(firstAnswerPage);
    },
    showAllAnswerBoxes(isSelectedAll) {
      if (isSelectedAll) {
        if (!_.isEmpty(this.answerItemMap)) {
          this.renderAllAnnotations();
        }
      } else {
        this.clearAnswerBoxes();
      }
    },
    hideAllAnswerBoxes() {
      this.clearAnswerBoxes();
    },
    removeWidgetsBySchemaNode() {
      this.clearAnswerBoxes();
    },
    goback() {
      window.history.go(-1);
    },
    keyDownListener(e) {
      if (e.keyCode === 81) {
        if (this.defaultAnnotationMode === 'wireframe') {
          this.defaultAnnotationMode = 'multiframe';
        } else {
          this.defaultAnnotationMode = 'wireframe';
        }
      }
    },
    onPageChange() {
      this.currentPageTableBoxes = [];
      this.defaultAnnotationMode = 'wireframe';
    },
    onPageRendered(event) {
      const pdfViewer = this.$refs.pdfViewer;
      if (event.pageNumber === pdfViewer.currentPage) {
        this.scrollToAnnotation(pdfViewer.$el);
        EventBus.$emit('view-page-rendered', event);
      }
    },
    async onChangeAnnotationMode(mode) {
      if (mode === 'table') {
        this.defaultAnnotationMode = 'table';
        const page = this.$refs.pdfViewer.currentPage - 1;
        const tables = await this.fetchTableElements(page);
        this.currentPageTableBoxes = formatTablesToBoxes(tables, page);
        this.renderTableBoxes(this.currentPageTableBoxes);
      } else {
        this.eventBus.$emit('render-page-boxes', []);
      }
    },
    async fetchTableElements(page) {
      try {
        const res = await fetchOutlines(this.$route.query.fileId, page);
        return res.data;
      } catch (error) {
        this.$notify({
          title: this.$t(`message['错误']`),
          message: error.message,
          type: 'error',
        });
      }
    },
    renderTableBoxes() {
      this.$refs.pdfViewer.setAnnotations(this.currentPageTableBoxes);
    },
    async afterTableCellMousedown({ coords, table }) {
      if (!this.canDrawFrame()) {
        this.eventBus.$emit('destroy-rendered-temp-annotation');
        return;
      }

      const { row, col } = coords;
      let cell = table.cells[`${row}_${col}`];
      if (!cell) {
        const mergedCell = table.merged.find((mergedList) =>
          mergedList.find((cellItem) => _.isEqual(cellItem, [row, col])),
        );
        if (mergedCell) {
          const [rowOrigin, colOrigin] = mergedCell[0];
          cell = table.cells[`${rowOrigin}_${colOrigin}`];
        }
      }

      if (cell) {
        const cellBox = {
          box: {
            box_left: cell.box[0],
            box_top: cell.box[1],
            box_right: cell.box[2],
            box_bottom: cell.box[3],
          },
          page: table.page,
          text: cell.text,
        };

        await this.wireframeDrawEnd([cellBox]);
      }
    },
    async onAnnotationClicked(annotation) {
      await this.wireframeDrawEnd(annotation.annotation.referringTo.boxes);
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .annotation-toolbar {
  top: 35%;
  right: 20px;
  left: auto;
  z-index: 20;
}

::v-deep .thumbnails-wrap {
  top: 100px;
  right: 65px;
  display: block;
  width: 60px;
  max-height: 70vh;
  overflow-x: hidden;
  overflow-y: auto;
  text-align: center;
  z-index: 99;

  .box {
    margin: 10px auto;
  }

  .combine-button {
    font-size: 14px;
  }
}

::v-deep .pageNum-container {
  min-width: 120px;

  .toolbarLabel {
    min-width: 50px;
    white-space: nowrap;
  }
}

::v-deep .toolbarViewer {
  .toolbarViewerLeft {
    .back-container {
      min-width: 80px;
    }

    .pageNum-container {
      min-width: 120px;
    }
  }

  .toolbarViewerMiddle {
    min-width: 45%;
    justify-content: center;
  }

  .toolbarButton {
    &.zoomOut,
    &.zoomIn {
      &::before {
        filter: brightness(0.6);
      }
    }
  }
}

::v-deep .toolbar {
  z-index: 999;
}

::v-deep .toolbar,
::v-deep .findbar {
  input {
    font-size: 12px;
  }
}

::v-deep .findbar {
  left: 131px;
}

::v-deep .splitToolbarButton {
  min-width: 65px;

  .toolbarButton.zoomOut,
  .toolbarButton.zoomIn {
    &::before {
      color: #b6b6b6;
      transform: scale(1);
    }
  }
}

::v-deep .dropdownToolbarButton {
  > select {
    color: #767676;
  }
}

::v-deep .draw-box {
  pointer-events: none;
}

::v-deep .annotation-box {
  &:hover {
    background: rgba(#9d9d9d, 0.1);
  }

  .tags {
    span {
      width: auto;

      & + span {
        margin-top: 0;
        border-top: 1px solid #666;
      }
    }
  }
}

::v-deep .handsontable {
  z-index: 5;

  tr {
    td {
      &:hover {
        background: rgba(#9d9d9d, 0.1);
      }
    }
  }
}

::v-deep .split-line-wrap {
  display: none;
}
</style>

<style lang="scss">
.pdfAppContainer {
  .outlineView {
    .treeItem {
      a {
        color: #fff;
      }
    }
  }

  .thumbnailView {
    box-sizing: border-box;
    overflow-x: hidden;
  }
}
</style>
