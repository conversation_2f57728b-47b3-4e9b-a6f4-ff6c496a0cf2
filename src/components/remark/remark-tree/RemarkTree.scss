$itemSelectedBGColor: #bbdcfd;
$itemHoverBGColor: tint($itemSelectedBGColor);
$itemBorderColor: darken($itemSelectedBGColor, 5%);
$iconPlaceholderWidth: 17px;

.remark-tree-list {
  height: 100%;
  overflow: auto;

  li.is-selected {
    background: $itemSelectedBGColor !important;
  }

  .answer-header {
    line-height: 2em;
    display: flex;
    align-items: baseline;

    .icon-placeholder {
      display: inline-block;
      width: $iconPlaceholderWidth;

      .answer-header-icon {
        transition: all 0.15s ease-in;

        &.is-opened {
          transform: rotate(90deg);
        }
      }
    }
  }

  .group-type-node {
    background: #ebf4ff;
  }

  .first-node .second-node.add-answer {
    .answer-name {
      color: #00ff00 !important;
    }
  }

  .first-node .second-node.diff-answer {
    .answer-name {
      color: red !important;
    }
  }

  .answer-item {
    list-style: none;

    &.add-answer .node-sticky {
      .answer-name {
        color: #00ff00 !important;
      }
    }

    &.diff-answer .node-sticky {
      .answer-name {
        color: red !important;
      }
    }

    &.schema-node-selected > .answer-header {
      background: $itemSelectedBGColor !important;
    }

    & > .answer-header:hover {
      background: $itemHoverBGColor;
    }

    .answer {
      .answer-items-text {
        margin-left: $iconPlaceholderWidth;
      }
    }

    .pagination-wrapper {
      padding-right: 20px;
      text-align: right;
    }

    .can-cloned {
      position: relative;
      min-height: 80px;
      margin: 0 30px;
      background: #f6f6f6;

      .toggle-group-arrow {
        cursor: pointer;
        transition: all 0.3s;
        transform: rotate(90deg);
        margin: -1px 5px 0 0;
        vertical-align: middle;
        &:hover {
          color: $--color-primary;
        }
      }

      &.collapsed {
        &::before {
          content: '已被折叠，点击左侧箭头展开';
          display: block;
          position: absolute;
          top: 8px;
          width: 100%;
          height: 38px;
          line-height: 38px;
          margin-left: 5px;
          background-color: #f5f5f5;
          color: #d2d2d2;
          text-align: center;
          font-size: 12px;
        }
        .toggle-group-arrow {
          transform: rotate(0);
        }
        .answer-item-operation-btn,
        .answer-create-inline,
        .answer-item {
          display: none;
        }
      }

      .answer-index {
        position: absolute;
        left: -27px;
        top: 0;
        width: 28px;
        padding-top: 3px;
        text-align: center;
        list-style: none;
        .el-checkbox {
          display: block;
          margin-right: 5px;
          text-align: right;
        }
      }

      .index-number {
        width: 100%;
        font-size: 14px;
        font-weight: bold;

        &.is-new {
          &::after {
            content: '';
            position: absolute;
            right: 2px;
            top: 2px;
            display: block;
            width: 6px;
            height: 6px;
            border-radius: 3px;
            background-color: red;
          }
        }
      }

      .answer-item-operation-btn {
        position: absolute;
        z-index: 1;
        top: 0;
        right: -25px;

        display: flex;
        flex-flow: column;
        justify-content: center;
        align-items: center;
        row-gap: 7px;

        span {
          display: block;
          padding: 2px;
          border-radius: 4px;
          &:hover {
            background: #ebf4ff;
          }
        }

        i {
          color: #f56c6c;
        }
      }

      & [data-depth='3'] {
        padding-top: 10px;

        &:not(:last-of-type) {
          border-bottom: 1px solid #ccc;
        }

        .answer-create-inline {
          background: #f6f6f6;
        }
      }
    }

    .node-sticky {
      position: sticky;
      top: 0;
      right: 0;
      z-index: 3;
      &:not(.group-type-node) {
        background: #fff;
      }
    }

    .node-sticky-2 {
      position: sticky;
      top: 23px;
      right: 0;
      z-index: 2;
      background-color: #f6f6f6;
    }

    &.schema-node-exclude,
    .schema-node-collapse {
      margin: 0;
      padding: 0;
      height: 0;
      overflow: hidden;
    }
  }

  ul:not([data-depth='0']) > li > .answer-header {
    padding-left: 5px;
  }

  .answer-create-inline {
    display: flex;
    flex-flow: row;
    align-items: center;
    width: 100%;
    height: 32px;
    background: #fff;
    text-align: center;
    list-style: none;

    .create-trigger {
      flex: 1;
      margin: 4px 0;
      font-size: 24px;
      transform: scale(0);
      opacity: 0;
      transition: all ease 0.2s;

      &:hover {
        &:before {
          color: grey;
        }
      }
    }

    .table-remark-trigger {
      padding: 2px 5px;
      font-size: 12px;
      margin: 0 5px;
    }

    > .el-button {
      visibility: hidden;
    }

    &:hover {
      .create-trigger {
        opacity: 1;
        transform: scale(1);
      }
      > .el-button {
        visibility: visible;
      }
    }
  }
}
