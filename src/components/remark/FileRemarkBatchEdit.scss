.remark-batch-edit {
  display: flex;
  flex-flow: column;
  padding: 10px;
  width: calc(100% - 20px);
  height: 100%;
  background: #f6f6f6;

  .schema-node-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    span,
    i {
      font-size: 16px;
    }
    .el-icon-close {
      cursor: pointer;
      &:hover {
        color: #777;
      }
    }
  }

  .group-edit,
  .node-edit {
    flex: 1;
    overflow: auto;
    padding: 10px 0 20px 0;

    .el-select {
      width: 100%;
    }
  }

  .schema-node {
    list-style: none;

    li {
      &:hover {
        background: #eaeaea;
      }
    }

    &:not([data-level='1']) {
      margin-left: 20px;
    }
  }

  .schema-node-label {
    display: flex;
    flex-flow: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    padding: 4px 8px;

    &.active {
      background: #bbdcfd;
    }

    label {
      flex: 1;
    }

    &.sub {
      label {
        color: grey;
      }
    }
  }

  .schema-node-answer {
    margin-left: 20px;
    margin-bottom: 12px;
    width: calc(100% - 20px);

    li {
      display: flex;
      align-items: center;
      margin: 5px 0;
      list-style: none;
    }

    p {
      flex: 1;
      padding: 4px 8px;
      border: 1px solid #a2cffc;
      border-radius: 4px;
      background: #fff;

      &.active {
        background: #bbdcfd;
      }
    }

    i {
      padding: 8px;
      font-size: 16px;
      color: #f56c6c;
      cursor: pointer;
    }
  }

  .node-edit {
    .schema-node-answer {
      margin: 0;
      margin-top: 12px;
    }
  }

  .schema-enum-answer {
    margin-left: 20px;
    margin-bottom: 10px;
  }

  .remark-table-operation {
    display: flex;
    justify-content: flex-end;
    margin: 16px;
  }

  .node-edit-tip {
    margin-bottom: 10px;
    font-size: 12px;
    color: red;
  }
}

.batch-edit-select {
  .el-select-dropdown__wrap {
    max-height: 500px;
  }
}
