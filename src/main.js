import Vue from 'vue';
import VueRouter from 'vue-router';
import Vue<PERSON><PERSON><PERSON> from 'vue-kindergarten';

import ElementUI from 'element-ui';
import enLocale from 'element-ui/lib/locale/lang/en';

import 'element-ui/lib/theme-chalk/index.css';
import '@fortawesome/fontawesome-free-webfonts';
import '@fortawesome/fontawesome-free-webfonts/css/fa-solid.css';
import '@fortawesome/fontawesome-free-webfonts/css/fa-regular.css';
import '@paoding-label/vue-image-viewer/dist/common-image-viewer.css';
import './styles/global.css';

import echarts from 'echarts/lib/echarts';
require('./assets/echarts');

import '../svg.font.config';
import SvgFontIcon from './components/SvgFontIcon';
import ThemeIcon from './components/ThemeIcon';

import * as filters from './utils/filters';

import * as directives from './utils/directives';

import VueI18n from 'vue-i18n';
import messages from './i18n';

import { notify } from './utils/reset-elementui.js';

import App from './App.vue';
import router from 'env-router';
import store from './store';
import perimeters from './perimeters';
import { text, style } from './utils/globalDataByEnv';

const platformPerimeter = perimeters.find(
  (item) => item.purpose === 'platform',
);
if (platformPerimeter.isHkexEnv()) {
  import('./custom/hkex/style.scss');
} else if (platformPerimeter.isDefaultEnv()) {
  import('env-element-theme');
  import('./styles/default-style.scss');
  if (platformPerimeter.isNafmiiEnv()) {
    import('./custom/nafmii/styles/style.scss');
  }
  if (platformPerimeter.isCiticsDCMEnv()) {
    import('./custom/citics_dcm/styles/style.scss');
  }
  if (platformPerimeter.isCmfchinaEnv()) {
    import('./custom/cmfchina/styles/style.scss');
  }
}

Vue.use(VueI18n);
Vue.use(VueRouter);
Vue.use(ElementUI, {
  locale: process.env.VUE_APP_LANG === 'EN' ? enLocale : null,
});
Vue.use(VueKindergarten, {
  child: () => {
    return {
      ...store.getters.loginUser,
      config: store.getters.configuration,
    };
  },
});

const i18n = new VueI18n({
  locale: process.env.VUE_APP_LANG === 'EN' ? 'en' : 'cn',
  messages,
});

Vue.prototype.i18n = i18n;
Vue.prototype.$notify = notify;
Vue.prototype.$echarts = echarts;
Vue.prototype.$text = text;
Vue.prototype.$style = style;

Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});

Object.keys(directives).forEach((key) => {
  Vue.directive(key, directives[key]);
});

Vue.component('svg-font-icon', SvgFontIcon);
Vue.component('theme-icon', ThemeIcon);

const handleClick = ElementUI.Button.methods.handleClick;
ElementUI.Button.methods.handleClick = function (...arg) {
  this.$el.blur();
  handleClick.apply(this, arg);
};

(async () => {
  try {
    await store.dispatch('getConfig');
    await store.dispatch('getUserInfo');
  } catch (error) {
    if (error.status_code !== 401) {
      notify({
        title: i18n._vm.messages[i18n._vm['locale']].message['错误'],
        message: error.message,
        type: 'error',
      });
    }
  }

  new Vue({
    i18n,
    router,
    store,
    perimeters,
    render: (h) => h(App),
  }).$mount('#app');
})();
