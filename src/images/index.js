import uploadFile from './upload-file.svg';
import uploadZip from './upload-zip.svg';
import newFolder from './new-folder.svg';
import summary from './summary.svg';
import profile from './profile.svg';
import modelScore from './model-score.svg';
import rule from './rule.svg';
import issuerAnalysis from './issuer-analysis.svg';
import issuerExtract from './issuer-extract.svg';
import conflictIcon from './conflict-icon.svg';
import frame from './frame.png';
import AiPreset from './Ai-preset.png';
import downloadFile from './download-file.svg';
import exportFile from './export-file.svg';
import batchExport from './batch-export.svg';

export const AiPresetImg = AiPreset;
export const frameImg = frame;
export const uploadFileImg = uploadFile;
export const uploadZipImg = uploadZip;
export const newFolderImg = newFolder;
export const summaryImg = summary;
export const profileImg = profile;
export const modelScoreImg = modelScore;
export const ruleImg = rule;
export const issuerAnalysisImg = issuerAnalysis;
export const issuerExtractImg = issuerExtract;
export const conflictImg = conflictIcon;
export const downloadFileImg = downloadFile;
export const exportFileImg = exportFile;
export const batchExportImg = batchExport;
