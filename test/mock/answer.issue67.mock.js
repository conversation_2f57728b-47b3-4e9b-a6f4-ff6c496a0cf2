export const answer = {
  '4d0908fbffb3175d374b36f98f4cb1a2': {
    type: 'LRs',
    label: 'LRs',
    schemaPath: ['LRs'],
    md5: '4d0908fbffb3175d374b36f98f4cb1a2',
    attributes: [],
    items: [],
  },
  '55622a7fe414dbb873986be9ca331fe0': {
    label: 'A1',
    type: 'D/NS/N',
    words: 'A1 description',
    multiple: true,
    required: false,
    attributes: [
      { multiple: true, name: 'A1', required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          {
            components: [
              {
                frameData: {
                  height: '30.421099999999996',
                  id: 'page1:1540281280973',
                  left: '204',
                  page: 0,
                  top: '89.4737',
                  topleft: ['89.4737', '204'],
                  type: 'A1',
                  width: '52.789499999999975',
                },
                text: 'Font',
              },
            ],
            name: 'A1',
            label: 'Font',
          },
        ],
        schemaMD5: '55622a7fe414dbb873986be9ca331fe0',
        enumLabel: 'disclosure',
      },
    ],
    md5: '55622a7fe414dbb873986be9ca331fe0',
    schemaPath: ['LRs', 'A1'],
  },
  cbd73d3b857ae4d6907ec37b4502b6df: {
    type: 'D/NS/N',
    label: 'A2',
    schemaPath: ['LRs', 'A2'],
    md5: 'cbd73d3b857ae4d6907ec37b4502b6df',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A2', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        schemaMD5: 'cbd73d3b857ae4d6907ec37b4502b6df',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '35.7895',
                  top: '85.8947',
                  topleft: ['85.8947', '35.7895'],
                  width: '44.7368',
                  height: '34.8947',
                  type: 'A2',
                  id: 'page1:1540281354000',
                  page: 0,
                },
                text: 'Get',
              },
              {
                frameData: {
                  left: '153.8947',
                  top: '85.0000',
                  topleft: ['85.0000', '153.8947'],
                  width: '54.5789',
                  height: '37.5789',
                  type: 'A2',
                  id: 'page1:1540281356000',
                  page: 0,
                },
                text: 'w ith',
              },
            ],
            name: 'A2',
            label: 'Getw ith',
          },
        ],
      },
    ],
  },
  '4fdd42289a3935ebf0dce1859102466d': {
    type: 'D/NS/N',
    label: 'A3',
    schemaPath: ['LRs', 'A3'],
    md5: '4fdd42289a3935ebf0dce1859102466d',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A3', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '852ddfd43c27f4041daf0d6343401b56': {
    type: 'D/NS/N',
    label: 'A4',
    schemaPath: ['LRs', 'A4'],
    md5: '852ddfd43c27f4041daf0d6343401b56',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A4', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  f6c1d5cbd96bfee2501b32415a564dda: {
    type: 'D/NS/N',
    label: 'A5',
    schemaPath: ['LRs', 'A5'],
    md5: 'f6c1d5cbd96bfee2501b32415a564dda',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A5', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  fb154e94b84d507b40ddf6bd137937dc: {
    type: 'D/NS/N',
    label: 'A6',
    schemaPath: ['LRs', 'A6'],
    md5: 'fb154e94b84d507b40ddf6bd137937dc',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A6', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '14f853960458d8582fa87d245c0ca158': {
    type: 'D/NS/N',
    label: 'A7',
    schemaPath: ['LRs', 'A7'],
    md5: '14f853960458d8582fa87d245c0ca158',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A7', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '1c2abe3ba78ef57dbd6437941e28b9a0': {
    type: 'D/NS/N',
    label: 'A8',
    schemaPath: ['LRs', 'A8'],
    md5: '1c2abe3ba78ef57dbd6437941e28b9a0',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A8', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '9de1725bc2bf09e94ccdef80d87e5bda': {
    type: 'D/NS/N',
    label: 'A9',
    schemaPath: ['LRs', 'A9'],
    md5: '9de1725bc2bf09e94ccdef80d87e5bda',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A9', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  ee6df835798bd6f92eb956f51b401800: {
    type: 'A10',
    label: 'A10',
    schemaPath: ['LRs', 'A10'],
    md5: 'ee6df835798bd6f92eb956f51b401800',
    required: false,
    multiple: true,
    attributes: [
      {
        name: 'Name of every subsidiary',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: 'Country of operation',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: 'Country of incorporation',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: ' If incorporated in the PRC, the kind of legal entity',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: 'Particulars of the issued share capital',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: 'Debt securities of every subsidiary',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name: 'Name of every subsidiary',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: 'Country of operation',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: 'Country of incorporation',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: ' If incorporated in the PRC, the kind of legal entity',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: 'Particulars of the issued share capital',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: 'Debt securities of every subsidiary',
            type: 'D/NS/N',
          },
        ],
        schemaMD5: 'ee6df835798bd6f92eb956f51b401800',
      },
    ],
  },
  '0dac6231926962eacebc061b8e26c2ea': {
    type: 'A11',
    label: 'A11',
    schemaPath: ['LRs', 'A11'],
    md5: '0dac6231926962eacebc061b8e26c2ea',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A11.1', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A11.2', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A11.3', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A11.1', type: 'D/NS/N' },
          { components: [], label: '', name: 'A11.2', type: 'D/NS/N' },
          { components: [], label: '', name: 'A11.3', type: 'D/NS/N' },
        ],
        schemaMD5: '0dac6231926962eacebc061b8e26c2ea',
      },
    ],
  },
  '5f1a10b42e2c2aee40c8fae2223540a0': {
    type: 'A12',
    label: 'A12',
    schemaPath: ['LRs', 'A12'],
    md5: '5f1a10b42e2c2aee40c8fae2223540a0',
    required: false,
    multiple: true,
    attributes: [
      {
        name: 'List of directors',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      { name: 'Biography', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name: 'List of directors',
            type: 'D/NS/N',
          },
          { components: [], label: '', name: 'Biography', type: 'D/NS/N' },
        ],
        schemaMD5: '5f1a10b42e2c2aee40c8fae2223540a0',
      },
    ],
  },
  '276a3530e4308ad6f36dfc7810269a40': {
    type: 'D/NS/N',
    label: 'A13',
    schemaPath: ['LRs', 'A13'],
    md5: '276a3530e4308ad6f36dfc7810269a40',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A13', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  b7bcead16024b3ee5fba6f1091b2389f: {
    type: 'D/NS/N',
    label: 'A14',
    schemaPath: ['LRs', 'A14'],
    md5: 'b7bcead16024b3ee5fba6f1091b2389f',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A14', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  a234547b9489581e03fed3db1f3fa0a5: {
    type: 'D/NS/N',
    label: 'A15',
    schemaPath: ['LRs', 'A15'],
    md5: 'a234547b9489581e03fed3db1f3fa0a5',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A15', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  fbc22c13ad39f4d3c24dd2384400b9cc: {
    type: 'D/NS/N',
    label: 'A16',
    schemaPath: ['LRs', 'A16'],
    md5: 'fbc22c13ad39f4d3c24dd2384400b9cc',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A16', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '03b47234a87942e26ad5355a02dc85e9': {
    type: 'D/NS/N',
    label: 'A17',
    schemaPath: ['LRs', 'A17'],
    md5: '03b47234a87942e26ad5355a02dc85e9',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A17', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  e405bd7c51785bee36cc42d638e8cc88: {
    type: 'D/NS/N',
    label: 'A18',
    schemaPath: ['LRs', 'A18'],
    md5: 'e405bd7c51785bee36cc42d638e8cc88',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A18', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  b2b9c2e5ff83433b80cfcbbc0353dbdf: {
    type: 'A19',
    label: 'A19',
    schemaPath: ['LRs', 'A19'],
    md5: 'b2b9c2e5ff83433b80cfcbbc0353dbdf',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A19.1', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A19.2', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A19.1', type: 'D/NS/N' },
          { components: [], label: '', name: 'A19.2', type: 'D/NS/N' },
        ],
        schemaMD5: 'b2b9c2e5ff83433b80cfcbbc0353dbdf',
      },
    ],
  },
  '48e7d13479b4795a1246fbfad1115763': {
    type: 'D/NS/N',
    label: 'A20',
    schemaPath: ['LRs', 'A20'],
    md5: '48e7d13479b4795a1246fbfad1115763',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A20', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '74f8a83d40a5f2b5a592833ca1394045': {
    type: 'A21',
    label: 'A21',
    schemaPath: ['LRs', 'A21'],
    md5: '74f8a83d40a5f2b5a592833ca1394045',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A21.1', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A21.2', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A21.3', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A21.4', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A21.5', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A21.1', type: 'D/NS/N' },
          { components: [], label: '', name: 'A21.2', type: 'D/NS/N' },
          { components: [], label: '', name: 'A21.3', type: 'D/NS/N' },
          { components: [], label: '', name: 'A21.4', type: 'D/NS/N' },
          { components: [], label: '', name: 'A21.5', type: 'D/NS/N' },
        ],
        schemaMD5: '74f8a83d40a5f2b5a592833ca1394045',
      },
    ],
  },
  '6e6ed22156a145885dc528ab2fd109bd': {
    type: 'D/NS/N',
    label: 'A22',
    schemaPath: ['LRs', 'A22'],
    md5: '6e6ed22156a145885dc528ab2fd109bd',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A22', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  a1fc0713d58412148c94c9522bd11af4: {
    type: 'D/NS/N',
    label: 'A23',
    schemaPath: ['LRs', 'A23'],
    md5: 'a1fc0713d58412148c94c9522bd11af4',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A23', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  ce0c2bc50143e8e3f15ca93e19b26bcc: {
    type: 'A24',
    label: 'A24',
    schemaPath: ['LRs', 'A24'],
    md5: 'ce0c2bc50143e8e3f15ca93e19b26bcc',
    required: false,
    multiple: true,
    attributes: [
      {
        name: 'Table for related party transaction',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      { name: 'Details', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name: 'Table for related party transaction',
            type: 'D/NS/N',
          },
          { components: [], label: '', name: 'Details', type: 'D/NS/N' },
        ],
        schemaMD5: 'ce0c2bc50143e8e3f15ca93e19b26bcc',
      },
    ],
  },
  '6cdbd2846df7034f6bf62f57dc117403': {
    type: 'D/NS/N',
    label: 'A25',
    schemaPath: ['LRs', 'A25'],
    md5: '6cdbd2846df7034f6bf62f57dc117403',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A25', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '4f078c2d7aa5df5ab81fee975e777d7e': {
    type: 'D/NS/N',
    label: 'A26',
    schemaPath: ['LRs', 'A26'],
    md5: '4f078c2d7aa5df5ab81fee975e777d7e',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A26', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '7cda96ae6f0d4c4b45aa412b39e81b05': {
    type: 'D/NS/N',
    label: 'A27',
    schemaPath: ['LRs', 'A27'],
    md5: '7cda96ae6f0d4c4b45aa412b39e81b05',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A27', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '14876b40d5252b081bc9d341a8e3c67c': {
    type: 'A28',
    label: 'A28',
    schemaPath: ['LRs', 'A28'],
    md5: '14876b40d5252b081bc9d341a8e3c67c',
    required: false,
    multiple: true,
    attributes: [
      {
        name:
          'whether the annual reports disclosed the fulfilment of profit guarantee',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: ' Shortfall of the profit guarantee',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name:
          '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name:
              'whether the annual reports disclosed the fulfilment of profit guarantee',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: ' Shortfall of the profit guarantee',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name:
              '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
            type: 'D/NS/N',
          },
        ],
        schemaMD5: '14876b40d5252b081bc9d341a8e3c67c',
      },
    ],
  },
  '5fed68f9a2bb9f6e73e724201186f377': {
    type: 'A29',
    label: 'A29',
    schemaPath: ['LRs', 'A29'],
    md5: '5fed68f9a2bb9f6e73e724201186f377',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A29.1', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A29.2', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A29.3', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A29.1', type: 'D/NS/N' },
          { components: [], label: '', name: 'A29.2', type: 'D/NS/N' },
          { components: [], label: '', name: 'A29.3', type: 'D/NS/N' },
        ],
        schemaMD5: '5fed68f9a2bb9f6e73e724201186f377',
      },
    ],
  },
  '653fbb1322d34df21db22237f8b24ffa': {
    type: 'D/NS/N',
    label: 'A30',
    schemaPath: ['LRs', 'A30'],
    md5: '653fbb1322d34df21db22237f8b24ffa',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A30', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '57271076057cb476b14a8af6d75ed4ec': {
    type: 'D/NS/N',
    label: 'A31',
    schemaPath: ['LRs', 'A31'],
    md5: '57271076057cb476b14a8af6d75ed4ec',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A31', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '908786b89c9d422a3236ec0ad3abe1b3': {
    type: 'D/NS/N',
    label: 'A32',
    schemaPath: ['LRs', 'A32'],
    md5: '908786b89c9d422a3236ec0ad3abe1b3',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A32', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  fe1f96e934742fcd3f8c35c1035c29b1: {
    type: 'D/NS/N',
    label: 'A33',
    schemaPath: ['LRs', 'A33'],
    md5: 'fe1f96e934742fcd3f8c35c1035c29b1',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A33', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
};

export const schema = {
  schemas: [
    {
      name: 'LRs',
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
      ],
      schema: {
        A1: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A1 description',
          name: 'A1',
          _index: 3,
        },
        A2: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A2',
          _index: 4,
        },
        A3: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A3 description',
          name: 'A3',
          _index: 5,
        },
        A4: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A4',
          _index: 6,
        },
        A5: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A5',
          _index: 7,
        },
        A6: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A6',
          _index: 8,
        },
        A7: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A7',
          _index: 9,
        },
        A8: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A8',
          _index: 10,
        },
        A9: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A9',
          _index: 11,
        },
        A10: {
          type: 'A10',
          required: false,
          multi: true,
          words: 'A10 description',
          name: 'A10',
          _index: 12,
        },
        A11: {
          type: 'A11',
          required: false,
          multi: true,
          name: 'A11',
          _index: 13,
        },
        A12: {
          type: 'A12',
          required: false,
          multi: true,
          name: 'A12',
          _index: 14,
        },
        A13: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A13',
          _index: 15,
        },
        A14: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A14',
          _index: 16,
        },
        A15: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A15',
          _index: 17,
        },
        A16: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A16',
          _index: 18,
        },
        A17: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A17',
          _index: 19,
        },
        A18: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A18',
          _index: 20,
        },
        A19: {
          type: 'A19',
          required: false,
          multi: true,
          name: 'A19',
          _index: 21,
        },
        A20: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A20',
          _index: 22,
        },
        A21: {
          type: 'A21',
          required: false,
          multi: true,
          name: 'A21',
          _index: 23,
        },
        A22: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A22',
          _index: 24,
        },
        A23: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A23',
          _index: 25,
        },
        A24: {
          type: 'A24',
          required: false,
          multi: true,
          name: 'A24',
          _index: 26,
        },
        A25: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A25',
          _index: 27,
        },
        A26: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A26',
          _index: 28,
        },
        A27: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A27',
          _index: 29,
        },
        A28: {
          type: 'A28',
          required: false,
          multi: true,
          name: 'A28',
          _index: 30,
        },
        A29: {
          type: 'A29',
          required: false,
          multi: true,
          name: 'A29',
          _index: 31,
        },
        A30: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A30',
          _index: 32,
        },
        A31: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A31',
          _index: 33,
        },
        A32: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A32',
          _index: 34,
        },
        A33: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A33',
          _index: 35,
        },
      },
      words: 'root info',
    },
    {
      name: 'A6',
      orders: ['A6', 'CONDITION'],
      schema: {
        A6: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A6',
          _index: 37,
        },
        CONDITION: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 38,
        },
      },
    },
    {
      name: 'A7',
      orders: ['A7', 'CONDITION'],
      schema: {
        A7: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A7',
          _index: 40,
        },
        CONDITION: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 41,
        },
      },
    },
    {
      name: 'A9',
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.1': {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'A9.1',
          _index: 43,
        },
        'A9.2': {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'A9.2',
          _index: 44,
        },
      },
    },
    {
      name: 'A10',
      orders: [
        'Name of every subsidiary',
        'Country of operation',
        'Country of incorporation',
        ' If incorporated in the PRC, the kind of legal entity',
        'Particulars of the issued share capital',
        'Debt securities of every subsidiary',
      ],
      schema: {
        'Name of every subsidiary': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Name of every subsidiary',
          _index: 46,
        },
        'Country of operation': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'country of operation description',
          name: 'Country of operation',
          _index: 47,
        },
        'Country of incorporation': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Country of incorporation',
          _index: 48,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 49,
        },
        'Particulars of the issued share capital': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 50,
        },
        'Debt securities of every subsidiary': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Debt securities of every subsidiary',
          _index: 51,
        },
      },
    },
    {
      name: 'A12',
      orders: ['List of directors', 'Biography'],
      schema: {
        'List of directors': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'List of directors',
          _index: 53,
        },
        Biography: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Biography',
          _index: 54,
        },
      },
    },
    {
      name: 'A19',
      orders: ['A19.1', 'A19.2'],
      schema: {
        'A19.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A19.1',
          _index: 56,
        },
        'A19.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A19.2',
          _index: 57,
        },
      },
    },
    {
      name: 'A23',
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 59,
        },
        A23: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A23',
          _index: 60,
        },
      },
    },
    {
      name: 'A24',
      orders: ['Table for related party transaction', 'Details'],
      schema: {
        'Table for related party transaction': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Table for related party transaction',
          _index: 62,
        },
        Details: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Details',
          _index: 63,
        },
      },
    },
    {
      name: 'A25',
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 65,
        },
        A25: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A25',
          _index: 66,
        },
      },
    },
    {
      name: 'A26',
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 68,
        },
        A26: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A26',
          _index: 69,
        },
      },
    },
    {
      name: 'A27',
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 71,
        },
        A27: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A27',
          _index: 72,
        },
      },
    },
    {
      name: 'A28',
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        ' Shortfall of the profit guarantee',
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
      ],
      schema: {
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 74,
        },
        ' Shortfall of the profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 75,
        },
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 76,
        },
      },
    },
    { name: 'text', orders: [], schema: {} },
    {
      name: 'A29',
      orders: ['A29.1', 'A29.2', 'A29.3'],
      schema: {
        'A29.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.1',
          _index: 79,
        },
        'A29.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.2',
          _index: 80,
        },
        'A29.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.3',
          _index: 81,
        },
      },
    },
    {
      name: 'A11',
      orders: ['A11.1', 'A11.2', 'A11.3'],
      schema: {
        'A11.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.1',
          _index: 83,
        },
        'A11.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.2',
          _index: 84,
        },
        'A11.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.3',
          _index: 85,
        },
      },
    },
    {
      name: 'A21',
      orders: ['A21.1', 'A21.2', 'A21.3', 'A21.4', 'A21.5'],
      schema: {
        'A21.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.1',
          _index: 87,
        },
        'A21.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.2',
          _index: 88,
        },
        'A21.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.3',
          _index: 89,
        },
        'A21.4': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.4',
          _index: 90,
        },
        'A21.5': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.5',
          _index: 91,
        },
      },
    },
  ],
  schema_types: [
    {
      label: 'Y/N',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
      ],
      type: 'enum',
    },
    {
      label: 'Y/N/NA',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        { isDefault: false, name: 'N/A' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N/In',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
        { isDefault: false, name: 'Insufficient' },
      ],
      type: 'enum',
    },
    {
      label: 'P/F',
      values: [
        { isDefault: false, name: 'pass' },
        { isDefault: false, name: 'fail' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
      ],
      type: 'enum',
    },
  ],
  version: '472ec16b6d3cc4ff93196e181c15e837',
};
