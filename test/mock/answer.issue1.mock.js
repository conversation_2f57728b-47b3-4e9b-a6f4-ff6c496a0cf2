/**
 * 用户答案 v1 转换到 v2 时丢失的问题
 * http://localhost:8080/#/project/question/9218?fileId=345&schemaId=5
 */

export const answer = {
  '4d0908fbffb3175d374b36f98f4cb1a2': {
    type: 'LRs',
    label: 'LRs',
    schemaPath: ['LRs'],
    md5: '4d0908fbffb3175d374b36f98f4cb1a2',
    attributes: [],
    items: [],
  },
  '55622a7fe414dbb873986be9ca331fe0': {
    type: 'D/NS/N',
    label: 'A1',
    schemaPath: ['LRs', 'A1'],
    md5: '55622a7fe414dbb873986be9ca331fe0',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A1', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [],
        schemaMD5: '55622a7fe414dbb873986be9ca331fe0',
        enumLabel: 'disclosure',
      },
      {
        schemaMD5: '55622a7fe414dbb873986be9ca331fe0',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '181.8000',
                  top: '190.8000',
                  topleft: ['190.8000', '181.8000'],
                  width: '306.6000',
                  height: '74.4000',
                  type: 'A1',
                  id: 'page1:1539829635000',
                  page: 0,
                },
                text:
                  'SAMSUNG HSI DAILY (2X) LEVERAGED \n PRODUCT (Stock code: 7205)',
              },
            ],
            name: 'A1',
            label:
              'SAMSUNG HSI DAILY (2X) LEVERAGED \n PRODUCT (Stock code: 7205)',
          },
        ],
      },
    ],
  },
  cbd73d3b857ae4d6907ec37b4502b6df: {
    type: 'D/NS/N',
    label: 'A2',
    schemaPath: ['LRs', 'A2'],
    md5: 'cbd73d3b857ae4d6907ec37b4502b6df',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A2', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [],
        schemaMD5: 'cbd73d3b857ae4d6907ec37b4502b6df',
        enumLabel: 'negative statement',
      },
      {
        schemaMD5: 'cbd73d3b857ae4d6907ec37b4502b6df',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '181.2000',
                  top: '346.8000',
                  topleft: ['346.8000', '181.2000'],
                  width: '385.8000',
                  height: '72.0000',
                  type: 'A2',
                  id: 'page1:1539829647000',
                  page: 0,
                },
                text:
                  '(SUB-FUNDS OF SAMSUNG LEVERAGED AND \nINVERSE INVESTMENT PRODUCT SERIES) \nReports and Financial Statements \nFor the period from 10 March 2017',
              },
            ],
            name: 'A2',
            label:
              '(SUB-FUNDS OF SAMSUNG LEVERAGED AND \nINVERSE INVESTMENT PRODUCT SERIES) \nReports and Financial Statements \nFor the period from 10 March 2017',
          },
        ],
      },
    ],
  },
  '4fdd42289a3935ebf0dce1859102466d': {
    type: 'D/NS/N',
    label: 'A3',
    schemaPath: ['LRs', 'A3'],
    md5: '4fdd42289a3935ebf0dce1859102466d',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A3', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '852ddfd43c27f4041daf0d6343401b56': {
    type: 'D/NS/N',
    label: 'A4',
    schemaPath: ['LRs', 'A4'],
    md5: '852ddfd43c27f4041daf0d6343401b56',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A4', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  f6c1d5cbd96bfee2501b32415a564dda: {
    type: 'D/NS/N',
    label: 'A5',
    schemaPath: ['LRs', 'A5'],
    md5: 'f6c1d5cbd96bfee2501b32415a564dda',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A5', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  fb154e94b84d507b40ddf6bd137937dc: {
    type: 'D/NS/N',
    label: 'A6',
    schemaPath: ['LRs', 'A6'],
    md5: 'fb154e94b84d507b40ddf6bd137937dc',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A6', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '14f853960458d8582fa87d245c0ca158': {
    type: 'D/NS/N',
    label: 'A7',
    schemaPath: ['LRs', 'A7'],
    md5: '14f853960458d8582fa87d245c0ca158',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A7', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '1c2abe3ba78ef57dbd6437941e28b9a0': {
    type: 'D/NS/N',
    label: 'A8',
    schemaPath: ['LRs', 'A8'],
    md5: '1c2abe3ba78ef57dbd6437941e28b9a0',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A8', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '9de1725bc2bf09e94ccdef80d87e5bda': {
    type: 'D/NS/N',
    label: 'A9',
    schemaPath: ['LRs', 'A9'],
    md5: '9de1725bc2bf09e94ccdef80d87e5bda',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A9', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  ee6df835798bd6f92eb956f51b401800: {
    type: 'A10',
    label: 'A10',
    schemaPath: ['LRs', 'A10'],
    md5: 'ee6df835798bd6f92eb956f51b401800',
    required: false,
    multiple: true,
    attributes: [
      {
        name: 'Name of every subsidiary',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: 'Country of operation',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: 'Country of incorporation',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: ' If incorporated in the PRC, the kind of legal entity',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: 'Particulars of the issued share capital',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: 'Debt securities of every subsidiary',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name: 'Name of every subsidiary',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: 'Country of operation',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: 'Country of incorporation',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: ' If incorporated in the PRC, the kind of legal entity',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: 'Particulars of the issued share capital',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: 'Debt securities of every subsidiary',
            type: 'D/NS/N',
          },
        ],
        schemaMD5: 'ee6df835798bd6f92eb956f51b401800',
      },
    ],
  },
  '0dac6231926962eacebc061b8e26c2ea': {
    type: 'A11',
    label: 'A11',
    schemaPath: ['LRs', 'A11'],
    md5: '0dac6231926962eacebc061b8e26c2ea',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A11.1', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A11.2', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A11.3', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A11.1', type: 'D/NS/N' },
          { components: [], label: '', name: 'A11.2', type: 'D/NS/N' },
          { components: [], label: '', name: 'A11.3', type: 'D/NS/N' },
        ],
        schemaMD5: '0dac6231926962eacebc061b8e26c2ea',
      },
    ],
  },
  '5f1a10b42e2c2aee40c8fae2223540a0': {
    type: 'A12',
    label: 'A12',
    schemaPath: ['LRs', 'A12'],
    md5: '5f1a10b42e2c2aee40c8fae2223540a0',
    required: false,
    multiple: true,
    attributes: [
      {
        name: 'List of directors',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      { name: 'Biography', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name: 'List of directors',
            type: 'D/NS/N',
          },
          { components: [], label: '', name: 'Biography', type: 'D/NS/N' },
        ],
        schemaMD5: '5f1a10b42e2c2aee40c8fae2223540a0',
      },
    ],
  },
  '276a3530e4308ad6f36dfc7810269a40': {
    type: 'D/NS/N',
    label: 'A13',
    schemaPath: ['LRs', 'A13'],
    md5: '276a3530e4308ad6f36dfc7810269a40',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A13', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  b7bcead16024b3ee5fba6f1091b2389f: {
    type: 'D/NS/N',
    label: 'A14',
    schemaPath: ['LRs', 'A14'],
    md5: 'b7bcead16024b3ee5fba6f1091b2389f',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A14', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  a234547b9489581e03fed3db1f3fa0a5: {
    type: 'D/NS/N',
    label: 'A15',
    schemaPath: ['LRs', 'A15'],
    md5: 'a234547b9489581e03fed3db1f3fa0a5',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A15', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  fbc22c13ad39f4d3c24dd2384400b9cc: {
    type: 'D/NS/N',
    label: 'A16',
    schemaPath: ['LRs', 'A16'],
    md5: 'fbc22c13ad39f4d3c24dd2384400b9cc',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A16', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '03b47234a87942e26ad5355a02dc85e9': {
    type: 'D/NS/N',
    label: 'A17',
    schemaPath: ['LRs', 'A17'],
    md5: '03b47234a87942e26ad5355a02dc85e9',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A17', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  e405bd7c51785bee36cc42d638e8cc88: {
    type: 'D/NS/N',
    label: 'A18',
    schemaPath: ['LRs', 'A18'],
    md5: 'e405bd7c51785bee36cc42d638e8cc88',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A18', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  b2b9c2e5ff83433b80cfcbbc0353dbdf: {
    type: 'A19',
    label: 'A19',
    schemaPath: ['LRs', 'A19'],
    md5: 'b2b9c2e5ff83433b80cfcbbc0353dbdf',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A19.1', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A19.2', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A19.1', type: 'D/NS/N' },
          { components: [], label: '', name: 'A19.2', type: 'D/NS/N' },
        ],
        schemaMD5: 'b2b9c2e5ff83433b80cfcbbc0353dbdf',
      },
    ],
  },
  '48e7d13479b4795a1246fbfad1115763': {
    type: 'D/NS/N',
    label: 'A20',
    schemaPath: ['LRs', 'A20'],
    md5: '48e7d13479b4795a1246fbfad1115763',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A20', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '74f8a83d40a5f2b5a592833ca1394045': {
    type: 'A21',
    label: 'A21',
    schemaPath: ['LRs', 'A21'],
    md5: '74f8a83d40a5f2b5a592833ca1394045',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A21.1', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A21.2', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A21.3', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A21.4', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A21.5', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A21.1', type: 'D/NS/N' },
          { components: [], label: '', name: 'A21.2', type: 'D/NS/N' },
          { components: [], label: '', name: 'A21.3', type: 'D/NS/N' },
          { components: [], label: '', name: 'A21.4', type: 'D/NS/N' },
          { components: [], label: '', name: 'A21.5', type: 'D/NS/N' },
        ],
        schemaMD5: '74f8a83d40a5f2b5a592833ca1394045',
      },
    ],
  },
  '6e6ed22156a145885dc528ab2fd109bd': {
    type: 'D/NS/N',
    label: 'A22',
    schemaPath: ['LRs', 'A22'],
    md5: '6e6ed22156a145885dc528ab2fd109bd',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A22', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  a1fc0713d58412148c94c9522bd11af4: {
    type: 'D/NS/N',
    label: 'A23',
    schemaPath: ['LRs', 'A23'],
    md5: 'a1fc0713d58412148c94c9522bd11af4',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A23', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  ce0c2bc50143e8e3f15ca93e19b26bcc: {
    type: 'A24',
    label: 'A24',
    schemaPath: ['LRs', 'A24'],
    md5: 'ce0c2bc50143e8e3f15ca93e19b26bcc',
    required: false,
    multiple: true,
    attributes: [
      {
        name: 'Table for related party transaction',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      { name: 'Details', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name: 'Table for related party transaction',
            type: 'D/NS/N',
          },
          { components: [], label: '', name: 'Details', type: 'D/NS/N' },
        ],
        schemaMD5: 'ce0c2bc50143e8e3f15ca93e19b26bcc',
      },
    ],
  },
  '6cdbd2846df7034f6bf62f57dc117403': {
    type: 'D/NS/N',
    label: 'A25',
    schemaPath: ['LRs', 'A25'],
    md5: '6cdbd2846df7034f6bf62f57dc117403',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A25', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '4f078c2d7aa5df5ab81fee975e777d7e': {
    type: 'D/NS/N',
    label: 'A26',
    schemaPath: ['LRs', 'A26'],
    md5: '4f078c2d7aa5df5ab81fee975e777d7e',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A26', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '7cda96ae6f0d4c4b45aa412b39e81b05': {
    type: 'D/NS/N',
    label: 'A27',
    schemaPath: ['LRs', 'A27'],
    md5: '7cda96ae6f0d4c4b45aa412b39e81b05',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A27', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '14876b40d5252b081bc9d341a8e3c67c': {
    type: 'A28',
    label: 'A28',
    schemaPath: ['LRs', 'A28'],
    md5: '14876b40d5252b081bc9d341a8e3c67c',
    required: false,
    multiple: true,
    attributes: [
      {
        name:
          'whether the annual reports disclosed the fulfilment of profit guarantee',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: ' Shortfall of the profit guarantee',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name:
          '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name:
              'whether the annual reports disclosed the fulfilment of profit guarantee',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: ' Shortfall of the profit guarantee',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name:
              '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
            type: 'D/NS/N',
          },
        ],
        schemaMD5: '14876b40d5252b081bc9d341a8e3c67c',
      },
    ],
  },
  '5fed68f9a2bb9f6e73e724201186f377': {
    type: 'A29',
    label: 'A29',
    schemaPath: ['LRs', 'A29'],
    md5: '5fed68f9a2bb9f6e73e724201186f377',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A29.1', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A29.2', multiple: true, required: false, type: 'D/NS/N' },
      { name: 'A29.3', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A29.1', type: 'D/NS/N' },
          { components: [], label: '', name: 'A29.2', type: 'D/NS/N' },
          { components: [], label: '', name: 'A29.3', type: 'D/NS/N' },
        ],
        schemaMD5: '5fed68f9a2bb9f6e73e724201186f377',
      },
    ],
  },
  '653fbb1322d34df21db22237f8b24ffa': {
    type: 'D/NS/N',
    label: 'A30',
    schemaPath: ['LRs', 'A30'],
    md5: '653fbb1322d34df21db22237f8b24ffa',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A30', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '57271076057cb476b14a8af6d75ed4ec': {
    type: 'D/NS/N',
    label: 'A31',
    schemaPath: ['LRs', 'A31'],
    md5: '57271076057cb476b14a8af6d75ed4ec',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A31', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '908786b89c9d422a3236ec0ad3abe1b3': {
    type: 'D/NS/N',
    label: 'A32',
    schemaPath: ['LRs', 'A32'],
    md5: '908786b89c9d422a3236ec0ad3abe1b3',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A32', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  fe1f96e934742fcd3f8c35c1035c29b1: {
    type: 'D/NS/N',
    label: 'A33',
    schemaPath: ['LRs', 'A33'],
    md5: 'fe1f96e934742fcd3f8c35c1035c29b1',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A33', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
};

export const schema = {
  schemas: [
    {
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
      ],
      schema: {
        A16: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A16',
          _index: 381,
        },
        A4: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A4',
          _index: 369,
        },
        A1: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A1',
          _index: 366,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 372,
        },
        A27: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A27',
          _index: 392,
        },
        A19: {
          required: false,
          type: 'A19',
          multi: true,
          name: 'A19',
          _index: 384,
        },
        A15: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A15',
          _index: 380,
        },
        A24: {
          required: false,
          type: 'A24',
          multi: true,
          name: 'A24',
          _index: 389,
        },
        A20: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A20',
          _index: 385,
        },
        A32: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A32',
          _index: 397,
        },
        A31: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A31',
          _index: 396,
        },
        A33: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A33',
          _index: 398,
        },
        A11: {
          required: false,
          type: 'A11',
          multi: true,
          name: 'A11',
          _index: 376,
        },
        A26: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A26',
          _index: 391,
        },
        A9: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A9',
          _index: 374,
        },
        A29: {
          required: false,
          type: 'A29',
          multi: true,
          name: 'A29',
          _index: 394,
        },
        A23: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A23',
          _index: 388,
        },
        A14: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A14',
          _index: 379,
        },
        A17: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A17',
          _index: 382,
        },
        A18: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A18',
          _index: 383,
        },
        A22: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A22',
          _index: 387,
        },
        A5: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A5',
          _index: 370,
        },
        A10: {
          required: false,
          type: 'A10',
          multi: true,
          name: 'A10',
          _index: 375,
        },
        A28: {
          required: false,
          type: 'A28',
          multi: true,
          name: 'A28',
          _index: 393,
        },
        A30: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A30',
          _index: 395,
        },
        A21: {
          required: false,
          type: 'A21',
          multi: true,
          name: 'A21',
          _index: 386,
        },
        A2: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A2',
          _index: 367,
        },
        A12: {
          required: false,
          type: 'A12',
          multi: true,
          name: 'A12',
          _index: 377,
        },
        A3: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A3',
          _index: 368,
        },
        A8: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A8',
          _index: 373,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 371,
        },
        A13: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A13',
          _index: 378,
        },
        A25: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A25',
          _index: 390,
        },
      },
      name: 'LRs',
    },
    {
      orders: ['A6', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 401,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 400,
        },
      },
      name: 'A6',
    },
    {
      orders: ['A7', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 404,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 403,
        },
      },
      name: 'A7',
    },
    {
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.2': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.2',
          _index: 407,
        },
        'A9.1': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.1',
          _index: 406,
        },
      },
      name: 'A9',
    },
    {
      orders: [
        'Name of every subsidiary',
        'Country of operation',
        'Country of incorporation',
        ' If incorporated in the PRC, the kind of legal entity',
        'Particulars of the issued share capital',
        'Debt securities of every subsidiary',
      ],
      schema: {
        'Debt securities of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Debt securities of every subsidiary',
          _index: 414,
        },
        'Name of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Name of every subsidiary',
          _index: 409,
        },
        'Country of operation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of operation',
          _index: 410,
        },
        'Particulars of the issued share capital': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 413,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 412,
        },
        'Country of incorporation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of incorporation',
          _index: 411,
        },
      },
      name: 'A10',
    },
    {
      orders: ['List of directors', 'Biography'],
      schema: {
        'List of directors': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'List of directors',
          _index: 416,
        },
        Biography: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Biography',
          _index: 417,
        },
      },
      name: 'A12',
    },
    {
      orders: ['A19.1', 'A19.2'],
      schema: {
        'A19.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.1',
          _index: 419,
        },
        'A19.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.2',
          _index: 420,
        },
      },
      name: 'A19',
    },
    {
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 422,
        },
        A23: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A23',
          _index: 423,
        },
      },
      name: 'A23',
    },
    {
      orders: ['Table for related party transaction', 'Details'],
      schema: {
        Details: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Details',
          _index: 426,
        },
        'Table for related party transaction': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Table for related party transaction',
          _index: 425,
        },
      },
      name: 'A24',
    },
    {
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 428,
        },
        A25: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A25',
          _index: 429,
        },
      },
      name: 'A25',
    },
    {
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 431,
        },
        A26: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A26',
          _index: 432,
        },
      },
      name: 'A26',
    },
    {
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 434,
        },
        A27: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A27',
          _index: 435,
        },
      },
      name: 'A27',
    },
    {
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        ' Shortfall of the profit guarantee',
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
      ],
      schema: {
        ' Shortfall of the profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 438,
        },
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 437,
        },
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 439,
        },
      },
      name: 'A28',
    },
    { orders: [], schema: {}, name: 'text' },
    {
      orders: ['A29.1', 'A29.2', 'A29.3'],
      schema: {
        'A29.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.3',
          _index: 444,
        },
        'A29.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.1',
          _index: 442,
        },
        'A29.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.2',
          _index: 443,
        },
      },
      name: 'A29',
    },
    {
      orders: ['A11.1', 'A11.2', 'A11.3'],
      schema: {
        'A11.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.3',
          _index: 448,
        },
        'A11.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.2',
          _index: 447,
        },
        'A11.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.1',
          _index: 446,
        },
      },
      name: 'A11',
    },
    {
      orders: ['A21.1', 'A21.2', 'A21.3', 'A21.4', 'A21.5'],
      schema: {
        'A21.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.3',
          _index: 452,
        },
        'A21.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.2',
          _index: 451,
        },
        'A21.4': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.4',
          _index: 453,
        },
        'A21.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.1',
          _index: 450,
        },
        'A21.5': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.5',
          _index: 454,
        },
      },
      name: 'A21',
    },
  ],
  schema_types: [
    {
      label: 'Y/N',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
      ],
      type: 'enum',
    },
    {
      label: 'Y/N/NA',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        { isDefault: false, name: 'N/A' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N/In',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
        { isDefault: false, name: 'Insufficient' },
      ],
      type: 'enum',
    },
    {
      label: 'P/F',
      values: [
        { isDefault: false, name: 'pass' },
        { isDefault: false, name: 'fail' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
      ],
      type: 'enum',
    },
  ],
  version: 'dadc4cbce001c2db744a3f5b61600c4a',
};
