/** 在v1标注时，不画框，只勾选框，保存后，勾选的值会丢失(v1 -> v2 答案时，对于只有enumLabel的值不保存) */
export const answer = {
  '1f11eece3b0d2ce8769d79868800e781': {
    type: 'LRs - test',
    label: 'LRs - test',
    schemaPath: ['LRs - test'],
    md5: '1f11eece3b0d2ce8769d79868800e781',
    attributes: [],
    items: [],
  },
  '14fa40cb271ba9eec8bc36cbb188b23e': {
    type: 'D/NS/ND',
    label: 'A1',
    schemaPath: ['LRs - test', 'A1'],
    md5: '14fa40cb271ba9eec8bc36cbb188b23e',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A1', multiple: true, required: false, type: 'D/NS/ND' },
    ],
    items: [
      {
        fields: [],
        schemaMD5: '14fa40cb271ba9eec8bc36cbb188b23e',
        enumLabel: 'D',
      },
    ],
  },
  '44bfac82287c922950be5251c51d3176': {
    label: 'A2',
    type: 'A2',
    required: false,
    multi: true,
    words: '',
    attributes: [
      { multiple: true, name: 'A2.1', required: false, type: 'D/NS/ND' },
      { multiple: true, name: 'A2.2', required: false, type: 'D/NS/ND' },
      { multiple: true, name: 'A2.3', required: false, type: 'D/NS/ND' },
      { multiple: true, name: 'A2.4', required: false, type: 'D/NS/ND' },
      { multiple: true, name: 'A2.5', required: false, type: 'D/NS/ND' },
    ],
    items: [
      {
        fields: [
          {
            components: [
              {
                frameData: {
                  height: '108.35660000000001',
                  id: 'page26:1540366607828',
                  left: '70.6144',
                  page: 25,
                  top: '183.2323',
                  topleft: ['183.2323', '70.6144'],
                  type: 'A2.1',
                  width: '301.3288',
                },
                text:
                  'Erwin Steve Huang, aged 52, is a Non-executive Director and the Deputy Chairman \nof the Company. He was the Chief Executive Officer of the Company for the period \nfrom 24 April 2008 to 28 February 2010. Prior to joining the Group in March 2005, \nhe was a seasoned entrepreneur in different industries, including publishing, \neducation, telecom and information technology. Mr. Huang has built and developed \nmultiple companies in London, San Francisco, Tokyo and Hong Kong in the past \nyears. He holds double degrees in Science in Business Administration and Business \nAdministration and Management from Boston University, USA.',
              },
            ],
            label:
              'Erwin Steve Huang, aged 52, is a Non-executive Director and the Deputy Chairman \nof the Company. He was the Chief Executive Officer of the Company for the period \nfrom 24 April 2008 to 28 February 2010. Prior to joining the Group in March 2005, \nhe was a seasoned entrepreneur in different industries, including publishing, \neducation, telecom and information technology. Mr. Huang has built and developed \nmultiple companies in London, San Francisco, Tokyo and Hong Kong in the past \nyears. He holds double degrees in Science in Business Administration and Business \nAdministration and Management from Boston University, USA.',
            enumLabel: 'D',
            name: 'A2.1',
            type: 'D/NS/ND',
          },
          {
            components: [],
            label: '',
            enumLabel: false,
            name: 'A2.2',
            type: 'D/NS/ND',
          },
          {
            components: [],
            label: '',
            enumLabel: false,
            name: 'A2.3',
            type: 'D/NS/ND',
          },
          {
            components: [],
            label: '',
            enumLabel: false,
            name: 'A2.4',
            type: 'D/NS/ND',
          },
          {
            components: [],
            label: '',
            enumLabel: false,
            name: 'A2.5',
            type: 'D/NS/ND',
          },
        ],
        schemaMD5: '44bfac82287c922950be5251c51d3176',
      },
    ],
    md5: '44bfac82287c922950be5251c51d3176',
    schemaPath: ['LRs - test', 'A2'],
  },
  '606062aa7cb8a348e23fa50bd6693cb4': {
    type: 'A3',
    label: 'A3',
    schemaPath: ['LRs - test', 'A3'],
    md5: '606062aa7cb8a348e23fa50bd6693cb4',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A3.1', multiple: true, required: false, type: 'D/NS/ND' },
      { name: 'A3.2', multiple: true, required: false, type: 'D/NS/ND' },
      { name: 'A3.3', multiple: true, required: false, type: 'D/NS/ND' },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A3.1', type: 'D/NS/ND' },
          { components: [], label: '', name: 'A3.2', type: 'D/NS/ND' },
          { components: [], label: '', name: 'A3.3', type: 'D/NS/ND' },
        ],
        schemaMD5: '606062aa7cb8a348e23fa50bd6693cb4',
      },
    ],
  },
  a411046b1dc5ed4accc8e39e0774e7f5: {
    type: 'D/NS/ND',
    label: 'A4',
    schemaPath: ['LRs - test', 'A4'],
    md5: 'a411046b1dc5ed4accc8e39e0774e7f5',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A4', multiple: true, required: false, type: 'D/NS/ND' },
    ],
    items: [],
  },
  '5729ae06f2d472d658a12cd585c62fdd': {
    type: 'D/NS/ND',
    label: 'A5',
    schemaPath: ['LRs - test', 'A5'],
    md5: '5729ae06f2d472d658a12cd585c62fdd',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A5', multiple: true, required: false, type: 'D/NS/ND' },
    ],
    items: [],
  },
  '69dec07e13ee34724a28a44c5985b4e7': {
    type: 'D/NS/ND',
    label: 'A6',
    schemaPath: ['LRs - test', 'A6'],
    md5: '69dec07e13ee34724a28a44c5985b4e7',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A6', multiple: true, required: false, type: 'D/NS/ND' },
    ],
    items: [],
  },
  b9e93daea70db7749e239512e1726677: {
    type: 'D/NS/ND',
    label: 'A7',
    schemaPath: ['LRs - test', 'A7'],
    md5: 'b9e93daea70db7749e239512e1726677',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A7', multiple: true, required: false, type: 'D/NS/ND' },
    ],
    items: [],
  },
};
export const schema = {
  schemas: [
    {
      name: 'LRs - test',
      orders: ['A1', 'A2', 'A3', 'A4', 'A5', 'A6', 'A7'],
      schema: {
        A1: {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A1',
          _index: 60,
        },
        A2: {
          type: 'A2',
          required: false,
          multi: true,
          name: 'A2',
          _index: 61,
        },
        A3: {
          type: 'A3',
          required: false,
          multi: true,
          name: 'A3',
          _index: 62,
        },
        A4: {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A4',
          _index: 63,
        },
        A5: {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A5',
          _index: 64,
        },
        A6: {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A6',
          _index: 65,
        },
        A7: {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A7',
          _index: 66,
        },
      },
    },
    {
      name: 'A2',
      orders: ['A2.1', 'A2.2', 'A2.3', 'A2.4', 'A2.5'],
      schema: {
        'A2.1': {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A2.1',
          _index: 68,
        },
        'A2.2': {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A2.2',
          _index: 69,
        },
        'A2.3': {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A2.3',
          _index: 70,
        },
        'A2.4': {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A2.4',
          _index: 71,
        },
        'A2.5': {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A2.5',
          _index: 72,
        },
      },
    },
    {
      name: 'A3',
      orders: ['A3.1', 'A3.2', 'A3.3'],
      schema: {
        'A3.1': {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A3.1',
          _index: 74,
        },
        'A3.2': {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A3.2',
          _index: 75,
        },
        'A3.3': {
          type: 'D/NS/ND',
          required: false,
          multi: true,
          name: 'A3.3',
          _index: 76,
        },
      },
    },
  ],
  schema_types: [
    {
      label: 'D/NS/ND',
      values: [
        { name: 'D', isDefault: false },
        { name: 'NS', isDefault: false },
        { name: 'ND', isDefault: false },
      ],
      type: 'enum',
    },
  ],
  version: '1bb5a0f9362a2615660a993fcd3b3b54',
};
