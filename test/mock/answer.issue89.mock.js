export const answer = {
  items: [
    {
      key: '["LRs","A1"]',
      schema: {
        data: {
          label: 'A1',
          type: 'D/NS/N',
          multiple: true,
          required: false,
          words: 'A1 description',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 32.6075,
                box_top: 269.2012,
                box_right: 251.0018,
                box_bottom: 290.43399999999997,
              },
              page: 2,
              text: 'Independent Non-executive Directors',
            },
            {
              box: {
                box_left: 338.9661,
                box_top: 271.4762,
                box_right: 426.1721,
                box_bottom: 288.1591,
              },
              page: 2,
              text: '獨立非執行董事',
            },
          ],
          value: '',
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 34.1241,
                box_top: 215.361,
                box_right: 176.687,
                box_bottom: 227.494,
              },
              page: 2,
              text: 'Non-executive Directors',
            },
            {
              box: {
                box_left: 332.8996,
                box_top: 216.1193,
                box_right: 400.3895,
                box_bottom: 226.7357,
              },
              page: 2,
              text: '非執行董事',
            },
          ],
          value: '',
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 334.4162,
                box_top: 138.7713,
                box_right: 389.0148,
                box_bottom: 154.6959,
              },
              page: 2,
              text: '董事會',
            },
          ],
          value: '',
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 34.1241,
                box_top: 136.4964,
                box_right: 200.953,
                box_bottom: 153.17929999999998,
              },
              page: 2,
              text: 'BOARD OF DIRECTORS',
            },
          ],
          value: '',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A2"]',
      schema: {
        data: {
          label: 'A2',
          type: 'D/NS/N',
          multiple: true,
          required: false,
          words: 'A2 description',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 335.1745,
                box_top: 516.4114,
                box_right: 404.18100000000004,
                box_bottom: 530.0609999999999,
              },
              page: 2,
              text: '名譽主席',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A10","Name of every subsidiary"]',
      schema: {
        data: {
          label: 'Name of every subsidiary',
          required: false,
          type: 'D/NS/N',
          words: 'name of every sub discription',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A10","Country of operation"]',
      schema: {
        data: {
          label: 'Country of operation',
          required: false,
          type: 'D/NS/N',
          words: 'country of operation description',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A10","Country of incorporation"]',
      schema: {
        data: {
          label: 'Country of incorporation',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key:
        '["LRs","A10"," If incorporated in the PRC, the kind of legal entity"]',
      schema: {
        data: {
          label: ' If incorporated in the PRC, the kind of legal entity',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A10","Particulars of the issued share capital"]',
      schema: {
        data: {
          label: 'Particulars of the issued share capital',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A10","Debt securities of every subsidiary"]',
      schema: {
        data: {
          label: 'Debt securities of every subsidiary',
          required: false,
          type: 'D/NS/N',
          words: 'debt securities of every subsidiary',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A11","A11.1"]',
      schema: {
        data: { label: 'A11.1', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A11', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A11'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A11","A11.2"]',
      schema: {
        data: { label: 'A11.2', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A11', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A11'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A11","A11.3"]',
      schema: {
        data: { label: 'A11.3', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A11', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A11'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A12","List of directors"]',
      schema: {
        data: {
          label: 'List of directors',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A12', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A12'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A12","Biography"]',
      schema: {
        data: {
          label: 'Biography',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A12', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A12'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A19","A19.1"]',
      schema: {
        data: { label: 'A19.1', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A19', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A19'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A19","A19.2"]',
      schema: {
        data: { label: 'A19.2', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A19', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A19'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A21","A21.1"]',
      schema: {
        data: { label: 'A21.1', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A21', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A21'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A21","A21.2"]',
      schema: {
        data: { label: 'A21.2', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A21', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A21'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A21","A21.3"]',
      schema: {
        data: { label: 'A21.3', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A21', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A21'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A21","A21.4"]',
      schema: {
        data: { label: 'A21.4', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A21', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A21'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A21","A21.5"]',
      schema: {
        data: { label: 'A21.5', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A21', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A21'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A24","Table for related party transaction"]',
      schema: {
        data: {
          label: 'Table for related party transaction',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A24', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A24'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A24","Details"]',
      schema: {
        data: { label: 'Details', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A24', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A24'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key:
        '["LRs","A28","whether the annual reports disclosed the fulfilment of profit guarantee"]',
      schema: {
        data: {
          label:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A28', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A28'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A28"," Shortfall of the profit guarantee"]',
      schema: {
        data: {
          label: ' Shortfall of the profit guarantee',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A28', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A28'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key:
        '["LRs","A28","  disclosure of whether the counterparty compensates the shortfall of profit guarantee"]',
      schema: {
        data: {
          label:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A28', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A28'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A29","A29.1"]',
      schema: {
        data: { label: 'A29.1', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A29', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A29'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A29","A29.2"]',
      schema: {
        data: { label: 'A29.2', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A29', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A29'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
    {
      key: '["LRs","A29","A29.3"]',
      schema: {
        data: { label: 'A29.3', required: false, type: 'D/NS/N', words: '' },
        meta: {
          _path: ['LRs', 'A29', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A29'],
        },
        children: [],
      },
      data: [{ boxes: [], value: '', handleType: 'wireframe' }],
    },
  ],
};
export const schema = {
  schemas: [
    {
      name: 'LRs',
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
        'A34',
      ],
      schema: {
        A1: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A1 description',
          name: 'A1',
          _index: 3,
        },
        A2: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A2 description',
          name: 'A2',
          _index: 4,
        },
        A3: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A3 description',
          name: 'A3',
          _index: 5,
        },
        A4: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A4 description',
          name: 'A4',
          _index: 6,
        },
        A5: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A5描述信息',
          name: 'A5',
          _index: 7,
        },
        A6: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A6',
          _index: 8,
        },
        A7: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A7',
          _index: 9,
        },
        A8: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A8',
          _index: 10,
        },
        A9: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A9',
          _index: 11,
        },
        A10: {
          type: 'A10',
          required: false,
          multi: true,
          words: 'A10 description',
          name: 'A10',
          _index: 12,
        },
        A11: {
          type: 'A11',
          required: false,
          multi: true,
          name: 'A11',
          _index: 13,
        },
        A12: {
          type: 'A12',
          required: false,
          multi: true,
          name: 'A12',
          _index: 14,
        },
        A13: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A13',
          _index: 15,
        },
        A14: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A14',
          _index: 16,
        },
        A15: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A15',
          _index: 17,
        },
        A16: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A16',
          _index: 18,
        },
        A17: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A17',
          _index: 19,
        },
        A18: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A18',
          _index: 20,
        },
        A19: {
          type: 'A19',
          required: false,
          multi: true,
          name: 'A19',
          _index: 21,
        },
        A20: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A20',
          _index: 22,
        },
        A21: {
          type: 'A21',
          required: false,
          multi: true,
          name: 'A21',
          _index: 23,
        },
        A22: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A22',
          _index: 24,
        },
        A23: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A23',
          _index: 25,
        },
        A24: {
          type: 'A24',
          required: false,
          multi: true,
          name: 'A24',
          _index: 26,
        },
        A25: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A25',
          _index: 27,
        },
        A26: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A26',
          _index: 28,
        },
        A27: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A27',
          _index: 29,
        },
        A28: {
          type: 'A28',
          required: false,
          multi: true,
          name: 'A28',
          _index: 30,
        },
        A29: {
          type: 'A29',
          required: false,
          multi: true,
          name: 'A29',
          _index: 31,
        },
        A30: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A30',
          _index: 32,
        },
        A31: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A31',
          _index: 33,
        },
        A32: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A32',
          _index: 34,
        },
        A33: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A33',
          _index: 35,
        },
        A34: {
          type: '文本',
          required: false,
          multi: false,
          name: 'A34',
          _index: 36,
        },
      },
      words: 'root info',
    },
    {
      name: 'A6',
      orders: ['A6', 'CONDITION'],
      schema: {
        A6: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A6',
          _index: 38,
        },
        CONDITION: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 39,
        },
      },
    },
    {
      name: 'A7',
      orders: ['A7', 'CONDITION'],
      schema: {
        A7: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A7',
          _index: 41,
        },
        CONDITION: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 42,
        },
      },
    },
    {
      name: 'A9',
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.1': {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'A9.1',
          _index: 44,
        },
        'A9.2': {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'A9.2',
          _index: 45,
        },
      },
    },
    {
      name: 'A10',
      orders: [
        'Name of every subsidiary',
        'Country of operation',
        'Country of incorporation',
        ' If incorporated in the PRC, the kind of legal entity',
        'Particulars of the issued share capital',
        'Debt securities of every subsidiary',
      ],
      schema: {
        'Name of every subsidiary': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'name of every sub discription',
          name: 'Name of every subsidiary',
          _index: 47,
        },
        'Country of operation': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'country of operation description',
          name: 'Country of operation',
          _index: 48,
        },
        'Country of incorporation': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Country of incorporation',
          _index: 49,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 50,
        },
        'Particulars of the issued share capital': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 51,
        },
        'Debt securities of every subsidiary': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'debt securities of every subsidiary',
          name: 'Debt securities of every subsidiary',
          _index: 52,
        },
      },
    },
    {
      name: 'A12',
      orders: ['List of directors', 'Biography'],
      schema: {
        'List of directors': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'List of directors',
          _index: 54,
        },
        Biography: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Biography',
          _index: 55,
        },
      },
    },
    {
      name: 'A19',
      orders: ['A19.1', 'A19.2'],
      schema: {
        'A19.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A19.1',
          _index: 57,
        },
        'A19.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A19.2',
          _index: 58,
        },
      },
    },
    {
      name: 'A23',
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 60,
        },
        A23: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A23',
          _index: 61,
        },
      },
    },
    {
      name: 'A24',
      orders: ['Table for related party transaction', 'Details'],
      schema: {
        'Table for related party transaction': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Table for related party transaction',
          _index: 63,
        },
        Details: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Details',
          _index: 64,
        },
      },
    },
    {
      name: 'A25',
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 66,
        },
        A25: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A25',
          _index: 67,
        },
      },
    },
    {
      name: 'A26',
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 69,
        },
        A26: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A26',
          _index: 70,
        },
      },
    },
    {
      name: 'A27',
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 72,
        },
        A27: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A27',
          _index: 73,
        },
      },
    },
    {
      name: 'A28',
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        ' Shortfall of the profit guarantee',
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
      ],
      schema: {
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 75,
        },
        ' Shortfall of the profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 76,
        },
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 77,
        },
      },
    },
    { name: 'text', orders: [], schema: {} },
    {
      name: 'A29',
      orders: ['A29.1', 'A29.2', 'A29.3'],
      schema: {
        'A29.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.1',
          _index: 80,
        },
        'A29.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.2',
          _index: 81,
        },
        'A29.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.3',
          _index: 82,
        },
      },
    },
    {
      name: 'A11',
      orders: ['A11.1', 'A11.2', 'A11.3'],
      schema: {
        'A11.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.1',
          _index: 84,
        },
        'A11.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.2',
          _index: 85,
        },
        'A11.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.3',
          _index: 86,
        },
      },
    },
    {
      name: 'A21',
      orders: ['A21.1', 'A21.2', 'A21.3', 'A21.4', 'A21.5'],
      schema: {
        'A21.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.1',
          _index: 88,
        },
        'A21.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.2',
          _index: 89,
        },
        'A21.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.3',
          _index: 90,
        },
        'A21.4': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.4',
          _index: 91,
        },
        'A21.5': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.5',
          _index: 92,
        },
      },
    },
  ],
  schema_types: [
    {
      label: 'Y/N',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
      ],
      type: 'enum',
    },
    {
      label: 'Y/N/NA',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        { isDefault: false, name: 'N/A' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N/In',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
        { isDefault: false, name: 'Insufficient' },
      ],
      type: 'enum',
    },
    {
      label: 'P/F',
      values: [
        { isDefault: false, name: 'pass' },
        { isDefault: false, name: 'fail' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
      ],
      type: 'enum',
    },
  ],
  version: 'ccc1250f5e26c24c957a93d04094d75c',
};
