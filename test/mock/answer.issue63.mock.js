export const answer = {
  items: [
    {
      key: '["A1-A33","A1"]',
      schema: {
        data: {
          label: 'A1',
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['A1-A33', 'Yes/No/Not Applicable'],
          _index: 492,
          _partType: 'top',
          _type: {
            values: [
              { isDefault: false, name: 'Yes' },
              { isDefault: false, name: 'No' },
              { isDefault: true, name: 'N/A' },
            ],
            type: 'enum',
            label: 'Yes/No/Not Applicable',
          },
          _isHide: false,
          _nodeIndex: 1002,
          _parent: ['A1-A33'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 28.4160094637224,
                box_top: 80.11987381703472,
                box_right: 156.80086750788647,
                box_bottom: 117.7665615141956,
              },
              page: 0,
              text: 'Get started',
            },
          ],
          value: 'Yes',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["A1-A33","A2"]',
      schema: {
        data: {
          label: 'A2',
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['A1-A33', 'Yes/No/Not Applicable'],
          _index: 493,
          _partType: 'top',
          _type: {
            values: [
              { isDefault: false, name: 'Yes' },
              { isDefault: false, name: 'No' },
              { isDefault: true, name: 'N/A' },
            ],
            type: 'enum',
            label: 'Yes/No/Not Applicable',
          },
          _isHide: false,
          _nodeIndex: 1003,
          _parent: ['A1-A33'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 205.0658517350158,
                box_top: 85.91167192429023,
                box_right: 257.1920347003155,
                box_bottom: 117.7665615141956,
              },
              page: 0,
              text: 'Font',
            },
            {
              box: {
                box_left: 262.983832807571,
                box_top: 83.98107255520506,
                box_right: 362.4097003154575,
                box_bottom: 118.73186119873819,
              },
              page: 0,
              text: 'Awesome',
            },
          ],
          value: 'Yes',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["A1-A33","A3"]',
      schema: {
        data: {
          label: 'A3',
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['A1-A33', 'Yes/No/Not Applicable'],
          _index: 633,
          _partType: 'top',
          _type: {
            values: [
              { isDefault: false, name: 'Yes' },
              { isDefault: false, name: 'No' },
              { isDefault: true, name: 'N/A' },
            ],
            type: 'enum',
            label: 'Yes/No/Not Applicable',
          },
          _isHide: false,
          _nodeIndex: 1004,
          _parent: ['A1-A33'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 57.37500000000001,
                box_top: 369.70977917981077,
                box_right: 173.21096214511044,
                box_bottom: 393.84227129337546,
              },
              page: 0,
              text: 'Web Fonts with CSS',
            },
          ],
          value: 'No',
          handleType: 'wireframe',
        },
      ],
    },
  ],
};
export const schema = {
  schemas: [
    {
      name: 'A1-A33',
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
      ],
      schema: {
        A1: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A1',
          _index: 597,
        },
        A2: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A2',
          _index: 598,
        },
        A3: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A3',
          _index: 599,
        },
        A4: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A4',
          _index: 600,
        },
        A5: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A5',
          _index: 601,
        },
        A6: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A6',
          _index: 602,
        },
        A7: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A7',
          _index: 603,
        },
        A8: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A8',
          _index: 604,
        },
        A9: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A9',
          _index: 605,
        },
        A10: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A10',
          _index: 606,
        },
        A11: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A11',
          _index: 607,
        },
        A12: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A12',
          _index: 608,
        },
        A13: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A13',
          _index: 609,
        },
        A14: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A14',
          _index: 610,
        },
        A15: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A15',
          _index: 611,
        },
        A16: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A16',
          _index: 612,
        },
        A17: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A17',
          _index: 613,
        },
        A18: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A18',
          _index: 614,
        },
        A19: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A19',
          _index: 615,
        },
        A20: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A20',
          _index: 616,
        },
        A21: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A21',
          _index: 617,
        },
        A22: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A22',
          _index: 618,
        },
        A23: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A23',
          _index: 619,
        },
        A24: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A24',
          _index: 620,
        },
        A25: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A25',
          _index: 621,
        },
        A26: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A26',
          _index: 622,
        },
        A27: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A27',
          _index: 623,
        },
        A28: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A28',
          _index: 624,
        },
        A29: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A29',
          _index: 625,
        },
        A30: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A30',
          _index: 626,
        },
        A31: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A31',
          _index: 627,
        },
        A32: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A32',
          _index: 628,
        },
        A33: {
          type: 'Yes/No/Not Applicable',
          required: false,
          multi: true,
          name: 'A33',
          _index: 629,
        },
      },
    },
  ],
  schema_types: [
    {
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        { isDefault: true, name: 'N/A' },
      ],
      type: 'enum',
      label: 'Yes/No/Not Applicable',
    },
  ],
  version: '9e95f6ed6e671a67a2dd9b259d6227f8',
};
