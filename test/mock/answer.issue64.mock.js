export const schema = {
  schemas: [
    {
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
      ],
      schema: {
        A16: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A16',
          _index: 381,
        },
        A4: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A4',
          _index: 369,
        },
        A1: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A1',
          _index: 366,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 372,
        },
        A27: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A27',
          _index: 392,
        },
        A19: {
          required: false,
          type: 'A19',
          multi: true,
          name: 'A19',
          _index: 384,
        },
        A15: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A15',
          _index: 380,
        },
        A24: {
          required: false,
          type: 'A24',
          multi: true,
          name: 'A24',
          _index: 389,
        },
        A20: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A20',
          _index: 385,
        },
        A32: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A32',
          _index: 397,
        },
        A31: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A31',
          _index: 396,
        },
        A33: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A33',
          _index: 398,
        },
        A11: {
          required: false,
          type: 'A11',
          multi: true,
          name: 'A11',
          _index: 376,
        },
        A26: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A26',
          _index: 391,
        },
        A9: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A9',
          _index: 374,
        },
        A29: {
          required: false,
          type: 'A29',
          multi: true,
          name: 'A29',
          _index: 394,
        },
        A23: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A23',
          _index: 388,
        },
        A14: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A14',
          _index: 379,
        },
        A17: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A17',
          _index: 382,
        },
        A18: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A18',
          _index: 383,
        },
        A22: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A22',
          _index: 387,
        },
        A5: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A5',
          _index: 370,
        },
        A10: {
          required: false,
          type: 'A10',
          multi: true,
          name: 'A10',
          _index: 375,
        },
        A28: {
          required: false,
          type: 'A28',
          multi: true,
          name: 'A28',
          _index: 393,
        },
        A30: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A30',
          _index: 395,
        },
        A21: {
          required: false,
          type: 'A21',
          multi: true,
          name: 'A21',
          _index: 386,
        },
        A2: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A2',
          _index: 367,
        },
        A12: {
          required: false,
          type: 'A12',
          multi: true,
          name: 'A12',
          _index: 377,
        },
        A3: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A3',
          _index: 368,
        },
        A8: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A8',
          _index: 373,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 371,
        },
        A13: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A13',
          _index: 378,
        },
        A25: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A25',
          _index: 390,
        },
      },
      name: 'LRs',
    },
    {
      orders: ['A6', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 401,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 400,
        },
      },
      name: 'A6',
    },
    {
      orders: ['A7', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 404,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 403,
        },
      },
      name: 'A7',
    },
    {
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.2': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.2',
          _index: 407,
        },
        'A9.1': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.1',
          _index: 406,
        },
      },
      name: 'A9',
    },
    {
      orders: [
        'Name of every subsidiary',
        'Country of operation',
        'Country of incorporation',
        ' If incorporated in the PRC, the kind of legal entity',
        'Particulars of the issued share capital',
        'Debt securities of every subsidiary',
      ],
      schema: {
        'Debt securities of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Debt securities of every subsidiary',
          _index: 414,
        },
        'Name of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Name of every subsidiary',
          _index: 409,
        },
        'Country of operation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of operation',
          _index: 410,
        },
        'Particulars of the issued share capital': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 413,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 412,
        },
        'Country of incorporation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of incorporation',
          _index: 411,
        },
      },
      name: 'A10',
    },
    {
      orders: ['List of directors', 'Biography'],
      schema: {
        'List of directors': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'List of directors',
          _index: 416,
        },
        Biography: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Biography',
          _index: 417,
        },
      },
      name: 'A12',
    },
    {
      orders: ['A19.1', 'A19.2'],
      schema: {
        'A19.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.1',
          _index: 419,
        },
        'A19.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.2',
          _index: 420,
        },
      },
      name: 'A19',
    },
    {
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 422,
        },
        A23: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A23',
          _index: 423,
        },
      },
      name: 'A23',
    },
    {
      orders: ['Table for related party transaction', 'Details'],
      schema: {
        Details: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Details',
          _index: 426,
        },
        'Table for related party transaction': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Table for related party transaction',
          _index: 425,
        },
      },
      name: 'A24',
    },
    {
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 428,
        },
        A25: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A25',
          _index: 429,
        },
      },
      name: 'A25',
    },
    {
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 431,
        },
        A26: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A26',
          _index: 432,
        },
      },
      name: 'A26',
    },
    {
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 434,
        },
        A27: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A27',
          _index: 435,
        },
      },
      name: 'A27',
    },
    {
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        ' Shortfall of the profit guarantee',
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
      ],
      schema: {
        ' Shortfall of the profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 438,
        },
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 437,
        },
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 439,
        },
      },
      name: 'A28',
    },
    { orders: [], schema: {}, name: 'text' },
    {
      orders: ['A29.1', 'A29.2', 'A29.3'],
      schema: {
        'A29.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.3',
          _index: 444,
        },
        'A29.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.1',
          _index: 442,
        },
        'A29.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.2',
          _index: 443,
        },
      },
      name: 'A29',
    },
    {
      orders: ['A11.1', 'A11.2', 'A11.3'],
      schema: {
        'A11.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.3',
          _index: 448,
        },
        'A11.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.2',
          _index: 447,
        },
        'A11.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.1',
          _index: 446,
        },
      },
      name: 'A11',
    },
    {
      orders: ['A21.1', 'A21.2', 'A21.3', 'A21.4', 'A21.5'],
      schema: {
        'A21.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.3',
          _index: 452,
        },
        'A21.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.2',
          _index: 451,
        },
        'A21.4': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.4',
          _index: 453,
        },
        'A21.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.1',
          _index: 450,
        },
        'A21.5': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.5',
          _index: 454,
        },
      },
      name: 'A21',
    },
  ],
  schema_types: [
    {
      label: 'Y/N',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
      ],
      type: 'enum',
    },
    {
      label: 'Y/N/NA',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        { isDefault: false, name: 'N/A' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N/In',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
        { isDefault: false, name: 'Insufficient' },
      ],
      type: 'enum',
    },
    {
      label: 'P/F',
      values: [
        { isDefault: false, name: 'pass' },
        { isDefault: false, name: 'fail' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
      ],
      type: 'enum',
    },
  ],
  version: 'dadc4cbce001c2db744a3f5b61600c4a',
};

export const answer = {
  items: [
    {
      key: '["LRs","A1"]',
      schema: {
        data: {
          label: 'A1',
          type: 'D/NS/N',
          words: '',
          multiple: true,
          required: false,
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 51.1755,
                box_top: 163.9351,
                box_right: 158.7308,
                box_bottom: 192.55870000000002,
              },
              page: 1,
              text: 'Corporate Information',
            },
          ],
          value: 'disclosure',
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 317.1075714071084,
                box_top: 166.25131318681312,
                box_right: 371.7097183851304,
                box_bottom: 189.07012087912082,
              },
              page: 1,
              text: '公司資料',
            },
          ],
          value: 'disclosure',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A2"]',
      schema: {
        data: {
          label: 'A2',
          type: 'D/NS/N',
          words: '',
          multiple: true,
          required: false,
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 317.92252882469086,
                box_top: 195.58978021978015,
                box_right: 367.6349312972183,
                box_bottom: 222.48337499999994,
              },
              page: 1,
              text: '主席報告',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A3"]',
      schema: {
        data: {
          label: 'A3',
          type: 'D/NS/N',
          words: '',
          multiple: true,
          required: false,
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 318.73748624227323,
                box_top: 329.2427967032966,
                box_right: 403.4930576708447,
                box_bottom: 348.80177472527464,
              },
              page: 1,
              text: '獨立核數師報告',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 320.3674010774381,
                box_top: 295.01458516483507,
                box_right: 376.59946289062486,
                box_bottom: 316.2034780219779,
              },
              page: 1,
              text: '董事會報告',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A10","Name of every subsidiary"]',
      schema: {
        data: {
          label: 'Name of every subsidiary',
          multiple: true,
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 47.78659851301114,
                box_top: 490.3640185873605,
                box_right: 216.1424609665427,
                box_bottom: 509.47865799256493,
              },
              page: 1,
              text: 'Consolidated Cash Flow Statement',
            },
            {
              box: {
                box_left: 313.92119330855013,
                box_top: 490.3640185873605,
                box_right: 424.9331375464683,
                box_bottom: 513.1545501858735,
              },
              page: 1,
              text: '綜合現金流量表',
            },
          ],
          value: 'disclosure',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A10","Country of operation"]',
      schema: {
        data: {
          label: 'Country of operation',
          multiple: true,
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 324.21369144981406,
                box_top: 584.4668587360593,
                box_right: 415.37581784386606,
                box_bottom: 607.2573903345724,
              },
              page: 1,
              text: '主要投資物業附表',
            },
          ],
          value: 'disclosure',
          handleType: 'wireframe',
        },
      ],
    },
  ],
};
