export const lrsSchema = {
  schemas: [
    {
      name: 'LRs',
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
      ],
      schema: {
        A1: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A1 description',
          name: 'A1',
          _index: 547,
        },
        A2: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A2',
          _index: 548,
        },
        A3: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A3 description',
          name: 'A3',
          _index: 549,
        },
        A4: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A4',
          _index: 550,
        },
        A5: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A5',
          _index: 551,
        },
        A6: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A6',
          _index: 552,
        },
        A7: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A7',
          _index: 553,
        },
        A8: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A8',
          _index: 554,
        },
        A9: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A9',
          _index: 555,
        },
        A10: {
          type: 'A10',
          required: false,
          multi: true,
          words: 'A10 description',
          name: 'A10',
          _index: 556,
        },
        A11: {
          type: 'A11',
          required: false,
          multi: true,
          name: 'A11',
          _index: 557,
        },
        A12: {
          type: 'A12',
          required: false,
          multi: true,
          name: 'A12',
          _index: 558,
        },
        A13: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A13',
          _index: 559,
        },
        A14: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A14',
          _index: 560,
        },
        A15: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A15',
          _index: 561,
        },
        A16: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A16',
          _index: 562,
        },
        A17: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A17',
          _index: 563,
        },
        A18: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A18',
          _index: 564,
        },
        A19: {
          type: 'A19',
          required: false,
          multi: true,
          name: 'A19',
          _index: 565,
        },
        A20: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A20',
          _index: 566,
        },
        A21: {
          type: 'A21',
          required: false,
          multi: true,
          name: 'A21',
          _index: 567,
        },
        A22: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A22',
          _index: 568,
        },
        A23: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A23',
          _index: 569,
        },
        A24: {
          type: 'A24',
          required: false,
          multi: true,
          name: 'A24',
          _index: 570,
        },
        A25: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A25',
          _index: 571,
        },
        A26: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A26',
          _index: 572,
        },
        A27: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A27',
          _index: 573,
        },
        A28: {
          type: 'A28',
          required: false,
          multi: true,
          name: 'A28',
          _index: 574,
        },
        A29: {
          type: 'A29',
          required: false,
          multi: true,
          name: 'A29',
          _index: 575,
        },
        A30: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A30',
          _index: 576,
        },
        A31: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A31',
          _index: 577,
        },
        A32: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A32',
          _index: 578,
        },
        A33: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A33',
          _index: 579,
        },
      },
      words: 'root info',
    },
    {
      name: 'A6',
      orders: ['A6', 'CONDITION'],
      schema: {
        A6: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A6',
          _index: 581,
        },
        CONDITION: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 582,
        },
      },
    },
    {
      name: 'A7',
      orders: ['A7', 'CONDITION'],
      schema: {
        A7: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A7',
          _index: 584,
        },
        CONDITION: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 585,
        },
      },
    },
    {
      name: 'A9',
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.1': {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'A9.1',
          _index: 587,
        },
        'A9.2': {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'A9.2',
          _index: 588,
        },
      },
    },
    {
      name: 'A10',
      orders: [
        'Name of every subsidiary',
        'Country of operation',
        'Country of incorporation',
        ' If incorporated in the PRC, the kind of legal entity',
        'Particulars of the issued share capital',
        'Debt securities of every subsidiary',
      ],
      schema: {
        'Name of every subsidiary': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Name of every subsidiary',
          _index: 590,
        },
        'Country of operation': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'country of operation description',
          name: 'Country of operation',
          _index: 591,
        },
        'Country of incorporation': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Country of incorporation',
          _index: 592,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 593,
        },
        'Particulars of the issued share capital': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 594,
        },
        'Debt securities of every subsidiary': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Debt securities of every subsidiary',
          _index: 595,
        },
      },
    },
    {
      name: 'A12',
      orders: ['List of directors', 'Biography'],
      schema: {
        'List of directors': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'List of directors',
          _index: 597,
        },
        Biography: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Biography',
          _index: 598,
        },
      },
    },
    {
      name: 'A19',
      orders: ['A19.1', 'A19.2'],
      schema: {
        'A19.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A19.1',
          _index: 600,
        },
        'A19.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A19.2',
          _index: 601,
        },
      },
    },
    {
      name: 'A23',
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 603,
        },
        A23: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A23',
          _index: 604,
        },
      },
    },
    {
      name: 'A24',
      orders: ['Table for related party transaction', 'Details'],
      schema: {
        'Table for related party transaction': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Table for related party transaction',
          _index: 606,
        },
        Details: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Details',
          _index: 607,
        },
      },
    },
    {
      name: 'A25',
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 609,
        },
        A25: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A25',
          _index: 610,
        },
      },
    },
    {
      name: 'A26',
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 612,
        },
        A26: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A26',
          _index: 613,
        },
      },
    },
    {
      name: 'A27',
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 615,
        },
        A27: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A27',
          _index: 616,
        },
      },
    },
    {
      name: 'A28',
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        ' Shortfall of the profit guarantee',
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
      ],
      schema: {
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 618,
        },
        ' Shortfall of the profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 619,
        },
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 620,
        },
      },
    },
    { name: 'text', orders: [], schema: {} },
    {
      name: 'A29',
      orders: ['A29.1', 'A29.2', 'A29.3'],
      schema: {
        'A29.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.1',
          _index: 623,
        },
        'A29.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.2',
          _index: 624,
        },
        'A29.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.3',
          _index: 625,
        },
      },
    },
    {
      name: 'A11',
      orders: ['A11.1', 'A11.2', 'A11.3'],
      schema: {
        'A11.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.1',
          _index: 627,
        },
        'A11.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.2',
          _index: 628,
        },
        'A11.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.3',
          _index: 629,
        },
      },
    },
    {
      name: 'A21',
      orders: ['A21.1', 'A21.2', 'A21.3', 'A21.4', 'A21.5'],
      schema: {
        'A21.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.1',
          _index: 631,
        },
        'A21.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.2',
          _index: 632,
        },
        'A21.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.3',
          _index: 633,
        },
        'A21.4': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.4',
          _index: 634,
        },
        'A21.5': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.5',
          _index: 635,
        },
      },
    },
  ],
  schema_types: [
    {
      label: 'Y/N',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
      ],
      type: 'enum',
    },
    {
      label: 'Y/N/NA',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        { isDefault: false, name: 'N/A' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N/In',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
        { isDefault: false, name: 'Insufficient' },
      ],
      type: 'enum',
    },
    {
      label: 'P/F',
      values: [
        { isDefault: false, name: 'pass' },
        { isDefault: false, name: 'fail' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
      ],
      type: 'enum',
    },
  ],
  version: '472ec16b6d3cc4ff93196e181c15e837',
};
