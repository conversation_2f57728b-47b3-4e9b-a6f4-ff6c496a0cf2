export default [
  {
    meta: {
      _index: 234,
      _partType: 'root',
      _type: { label: 'LRs', type: 'group' },
      _isHide: false,
      _nodeIndex: 1001,
      index: 0,
      _parent: [],
      _path: ['LRs'],
    },
    data: { label: 'LRs', type: 'LRs', words: 'root info' },
    children: [
      {
        data: {
          label: 'A1',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A1 description',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 235,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1002,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
        answer: {
          key: '["LRs:0","A1:0"]',
          schema: {
            data: {
              label: 'A1',
              type: 'D/NS/N',
              multiple: true,
              required: false,
              words: 'A1 description',
            },
            meta: {
              _path: ['LRs', 'D/NS/N'],
              _partType: 'top',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _parent: ['LRs'],
            },
            children: [],
          },
          data: [
            { boxes: [], handleType: 'wireframe', value: 'no disclosure' },
          ],
        },
      },
      {
        data: {
          label: 'A2',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 236,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1003,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A3',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A3 description',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 237,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1004,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A4',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 238,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1005,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A5',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 239,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1006,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A6',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 240,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1007,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A7',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 241,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1008,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A8',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 242,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1009,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A9',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 243,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1010,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A10',
          type: 'A10',
          required: false,
          multi: true,
          words: 'A10 description',
        },
        meta: {
          _path: ['LRs', 'A10'],
          _index: 244,
          _partType: 'top',
          _type: { label: 'A10', type: 'group' },
          _isHide: false,
          _nodeIndex: 1011,
          index: 0,
          _parent: ['LRs'],
        },
        children: [
          {
            data: {
              label: 'Name of every subsidiary',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 278,
              _path: ['LRs', 'A10', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1012,
              index: 0,
              _parent: ['LRs', 'A10'],
            },
            children: [],
            answer: {
              key: '["LRs:0","A10:0","Name of every subsidiary:0"]',
              schema: {
                data: {
                  label: 'Name of every subsidiary',
                  required: false,
                  type: 'D/NS/N',
                  words: '',
                },
                meta: {
                  _path: ['LRs', 'A10', 'D/NS/N'],
                  _partType: 'normal.schema',
                  _type: {
                    label: 'D/NS/N',
                    values: [
                      { isDefault: false, name: 'disclosure' },
                      { isDefault: false, name: 'negative statement' },
                      { isDefault: false, name: 'no disclosure' },
                    ],
                    type: 'enum',
                  },
                  _isHide: false,
                  _parent: ['LRs', 'A10'],
                },
                children: [],
              },
              data: [
                {
                  boxes: [
                    {
                      box: {
                        box_left: 47.75486020318021,
                        box_top: 144.43681507656063,
                        box_right: 200.60547033274437,
                        box_bottom: 168.97705064782093,
                      },
                      page: 3,
                      text: 'CORPORATE INFORMATION',
                    },
                    {
                      box: {
                        box_left: 294.55951509128386,
                        box_top: 145.13796466431094,
                        box_right: 384.3066623233215,
                        box_bottom: 165.47130270906948,
                      },
                      page: 3,
                      text: 'STOCK CODE',
                    },
                  ],
                  value: 'disclosure',
                  handleType: 'wireframe',
                },
              ],
            },
          },
          {
            data: {
              label: 'Country of operation',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: 'country of operation description',
            },
            meta: {
              _index: 279,
              _path: ['LRs', 'A10', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1013,
              index: 0,
              _parent: ['LRs', 'A10'],
            },
            children: [],
          },
          {
            data: {
              label: 'Country of incorporation',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 280,
              _path: ['LRs', 'A10', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1014,
              index: 0,
              _parent: ['LRs', 'A10'],
            },
            children: [],
          },
          {
            data: {
              label: ' If incorporated in the PRC, the kind of legal entity',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 281,
              _path: ['LRs', 'A10', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1015,
              index: 0,
              _parent: ['LRs', 'A10'],
            },
            children: [],
          },
          {
            data: {
              label: 'Particulars of the issued share capital',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 282,
              _path: ['LRs', 'A10', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1016,
              index: 0,
              _parent: ['LRs', 'A10'],
            },
            children: [],
          },
          {
            data: {
              label: 'Debt securities of every subsidiary',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 283,
              _path: ['LRs', 'A10', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1017,
              index: 0,
              _parent: ['LRs', 'A10'],
            },
            children: [],
          },
        ],
        childrenGroup: [
          [
            {
              data: {
                label: 'Name of every subsidiary',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 278,
                _path: ['LRs', 'A10', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1012,
                index: 0,
                _parent: ['LRs', 'A10'],
              },
              children: [],
              answer: {
                key: '["LRs:0","A10:0","Name of every subsidiary:0"]',
                schema: {
                  data: {
                    label: 'Name of every subsidiary',
                    required: false,
                    type: 'D/NS/N',
                    words: '',
                  },
                  meta: {
                    _path: ['LRs', 'A10', 'D/NS/N'],
                    _partType: 'normal.schema',
                    _type: {
                      label: 'D/NS/N',
                      values: [
                        { isDefault: false, name: 'disclosure' },
                        { isDefault: false, name: 'negative statement' },
                        { isDefault: false, name: 'no disclosure' },
                      ],
                      type: 'enum',
                    },
                    _isHide: false,
                    _parent: ['LRs', 'A10'],
                  },
                  children: [],
                },
                data: [
                  {
                    boxes: [
                      {
                        box: {
                          box_left: 47.75486020318021,
                          box_top: 144.43681507656063,
                          box_right: 200.60547033274437,
                          box_bottom: 168.97705064782093,
                        },
                        page: 3,
                        text: 'CORPORATE INFORMATION',
                      },
                      {
                        box: {
                          box_left: 294.55951509128386,
                          box_top: 145.13796466431094,
                          box_right: 384.3066623233215,
                          box_bottom: 165.47130270906948,
                        },
                        page: 3,
                        text: 'STOCK CODE',
                      },
                    ],
                    value: 'disclosure',
                    handleType: 'wireframe',
                  },
                ],
              },
            },
            {
              data: {
                label: 'Country of operation',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: 'country of operation description',
              },
              meta: {
                _index: 279,
                _path: ['LRs', 'A10', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1013,
                index: 0,
                _parent: ['LRs', 'A10'],
              },
              children: [],
            },
            {
              data: {
                label: 'Country of incorporation',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 280,
                _path: ['LRs', 'A10', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1014,
                index: 0,
                _parent: ['LRs', 'A10'],
              },
              children: [],
            },
            {
              data: {
                label: ' If incorporated in the PRC, the kind of legal entity',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 281,
                _path: ['LRs', 'A10', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1015,
                index: 0,
                _parent: ['LRs', 'A10'],
              },
              children: [],
            },
            {
              data: {
                label: 'Particulars of the issued share capital',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 282,
                _path: ['LRs', 'A10', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1016,
                index: 0,
                _parent: ['LRs', 'A10'],
              },
              children: [],
            },
            {
              data: {
                label: 'Debt securities of every subsidiary',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 283,
                _path: ['LRs', 'A10', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1017,
                index: 0,
                _parent: ['LRs', 'A10'],
              },
              children: [],
            },
          ],
        ],
      },
      {
        data: {
          label: 'A11',
          type: 'A11',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'A11'],
          _index: 245,
          _partType: 'top',
          _type: { label: 'A11', type: 'group' },
          _isHide: false,
          _nodeIndex: 1018,
          index: 0,
          _parent: ['LRs'],
        },
        children: [
          {
            data: {
              label: 'A11.1',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 315,
              _path: ['LRs', 'A11', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1019,
              index: 0,
              _parent: ['LRs', 'A11'],
            },
            children: [],
          },
          {
            data: {
              label: 'A11.2',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 316,
              _path: ['LRs', 'A11', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1020,
              index: 0,
              _parent: ['LRs', 'A11'],
            },
            children: [],
          },
          {
            data: {
              label: 'A11.3',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 317,
              _path: ['LRs', 'A11', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1021,
              index: 0,
              _parent: ['LRs', 'A11'],
            },
            children: [],
          },
        ],
        childrenGroup: [
          [
            {
              data: {
                label: 'A11.1',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 315,
                _path: ['LRs', 'A11', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1019,
                index: 0,
                _parent: ['LRs', 'A11'],
              },
              children: [],
            },
            {
              data: {
                label: 'A11.2',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 316,
                _path: ['LRs', 'A11', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1020,
                index: 0,
                _parent: ['LRs', 'A11'],
              },
              children: [],
            },
            {
              data: {
                label: 'A11.3',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 317,
                _path: ['LRs', 'A11', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1021,
                index: 0,
                _parent: ['LRs', 'A11'],
              },
              children: [],
            },
          ],
        ],
      },
      {
        data: {
          label: 'A12',
          type: 'A12',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'A12'],
          _index: 246,
          _partType: 'top',
          _type: { label: 'A12', type: 'group' },
          _isHide: false,
          _nodeIndex: 1022,
          index: 0,
          _parent: ['LRs'],
        },
        children: [
          {
            data: {
              label: 'List of directors',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 285,
              _path: ['LRs', 'A12', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1023,
              index: 0,
              _parent: ['LRs', 'A12'],
            },
            children: [],
          },
          {
            data: {
              label: 'Biography',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 286,
              _path: ['LRs', 'A12', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1024,
              index: 0,
              _parent: ['LRs', 'A12'],
            },
            children: [],
          },
        ],
        childrenGroup: [
          [
            {
              data: {
                label: 'List of directors',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 285,
                _path: ['LRs', 'A12', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1023,
                index: 0,
                _parent: ['LRs', 'A12'],
              },
              children: [],
            },
            {
              data: {
                label: 'Biography',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 286,
                _path: ['LRs', 'A12', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1024,
                index: 0,
                _parent: ['LRs', 'A12'],
              },
              children: [],
            },
          ],
          [
            {
              data: {
                label: 'List of directors',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 285,
                _path: ['LRs', 'A12', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 100001,
                index: 0,
                _parent: ['LRs', 'A12'],
              },
              children: [],
              uid: 1,
            },
            {
              data: {
                label: 'Biography',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 286,
                _path: ['LRs', 'A12', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 100002,
                index: 0,
                _parent: ['LRs', 'A12'],
              },
              children: [],
              uid: 2,
            },
          ],
          [
            {
              data: {
                label: 'List of directors',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 285,
                _path: ['LRs', 'A12', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 100003,
                index: 0,
                _parent: ['LRs', 'A12'],
              },
              children: [],
              uid: 3,
            },
            {
              data: {
                label: 'Biography',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 286,
                _path: ['LRs', 'A12', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 100004,
                index: 0,
                _parent: ['LRs', 'A12'],
              },
              children: [],
              uid: 4,
            },
          ],
        ],
      },
      {
        data: {
          label: 'A13',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 247,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1025,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A14',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 248,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1026,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A15',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 249,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1027,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A16',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 250,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1028,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A17',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 251,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1029,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A18',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 252,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1030,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A19',
          type: 'A19',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'A19'],
          _index: 253,
          _partType: 'top',
          _type: { label: 'A19', type: 'group' },
          _isHide: false,
          _nodeIndex: 1031,
          index: 0,
          _parent: ['LRs'],
        },
        children: [
          {
            data: {
              label: 'A19.1',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 288,
              _path: ['LRs', 'A19', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1032,
              index: 0,
              _parent: ['LRs', 'A19'],
            },
            children: [],
          },
          {
            data: {
              label: 'A19.2',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 289,
              _path: ['LRs', 'A19', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1033,
              index: 0,
              _parent: ['LRs', 'A19'],
            },
            children: [],
          },
        ],
        childrenGroup: [
          [
            {
              data: {
                label: 'A19.1',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 288,
                _path: ['LRs', 'A19', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1032,
                index: 0,
                _parent: ['LRs', 'A19'],
              },
              children: [],
            },
            {
              data: {
                label: 'A19.2',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 289,
                _path: ['LRs', 'A19', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1033,
                index: 0,
                _parent: ['LRs', 'A19'],
              },
              children: [],
            },
          ],
        ],
      },
      {
        data: {
          label: 'A20',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 254,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1034,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A21',
          type: 'A21',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'A21'],
          _index: 255,
          _partType: 'top',
          _type: { label: 'A21', type: 'group' },
          _isHide: false,
          _nodeIndex: 1035,
          index: 0,
          _parent: ['LRs'],
        },
        children: [
          {
            data: {
              label: 'A21.1',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 319,
              _path: ['LRs', 'A21', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1036,
              index: 0,
              _parent: ['LRs', 'A21'],
            },
            children: [],
          },
          {
            data: {
              label: 'A21.2',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 320,
              _path: ['LRs', 'A21', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1037,
              index: 0,
              _parent: ['LRs', 'A21'],
            },
            children: [],
          },
          {
            data: {
              label: 'A21.3',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 321,
              _path: ['LRs', 'A21', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1038,
              index: 0,
              _parent: ['LRs', 'A21'],
            },
            children: [],
          },
          {
            data: {
              label: 'A21.4',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 322,
              _path: ['LRs', 'A21', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1039,
              index: 0,
              _parent: ['LRs', 'A21'],
            },
            children: [],
          },
          {
            data: {
              label: 'A21.5',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 323,
              _path: ['LRs', 'A21', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1040,
              index: 0,
              _parent: ['LRs', 'A21'],
            },
            children: [],
          },
        ],
        childrenGroup: [
          [
            {
              data: {
                label: 'A21.1',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 319,
                _path: ['LRs', 'A21', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1036,
                index: 0,
                _parent: ['LRs', 'A21'],
              },
              children: [],
            },
            {
              data: {
                label: 'A21.2',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 320,
                _path: ['LRs', 'A21', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1037,
                index: 0,
                _parent: ['LRs', 'A21'],
              },
              children: [],
            },
            {
              data: {
                label: 'A21.3',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 321,
                _path: ['LRs', 'A21', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1038,
                index: 0,
                _parent: ['LRs', 'A21'],
              },
              children: [],
            },
            {
              data: {
                label: 'A21.4',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 322,
                _path: ['LRs', 'A21', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1039,
                index: 0,
                _parent: ['LRs', 'A21'],
              },
              children: [],
            },
            {
              data: {
                label: 'A21.5',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 323,
                _path: ['LRs', 'A21', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1040,
                index: 0,
                _parent: ['LRs', 'A21'],
              },
              children: [],
            },
          ],
        ],
      },
      {
        data: {
          label: 'A22',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 256,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1041,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A23',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 257,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1042,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A24',
          type: 'A24',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'A24'],
          _index: 258,
          _partType: 'top',
          _type: { label: 'A24', type: 'group' },
          _isHide: false,
          _nodeIndex: 1043,
          index: 0,
          _parent: ['LRs'],
        },
        children: [
          {
            data: {
              label: 'Table for related party transaction',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 294,
              _path: ['LRs', 'A24', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1044,
              index: 0,
              _parent: ['LRs', 'A24'],
            },
            children: [],
          },
          {
            data: {
              label: 'Details',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 295,
              _path: ['LRs', 'A24', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1045,
              index: 0,
              _parent: ['LRs', 'A24'],
            },
            children: [],
          },
        ],
        childrenGroup: [
          [
            {
              data: {
                label: 'Table for related party transaction',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 294,
                _path: ['LRs', 'A24', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1044,
                index: 0,
                _parent: ['LRs', 'A24'],
              },
              children: [],
            },
            {
              data: {
                label: 'Details',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 295,
                _path: ['LRs', 'A24', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1045,
                index: 0,
                _parent: ['LRs', 'A24'],
              },
              children: [],
            },
          ],
        ],
      },
      {
        data: {
          label: 'A25',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 259,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1046,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A26',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 260,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1047,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A27',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 261,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1048,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A28',
          type: 'A28',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'A28'],
          _index: 262,
          _partType: 'top',
          _type: { label: 'A28', type: 'group' },
          _isHide: false,
          _nodeIndex: 1049,
          index: 0,
          _parent: ['LRs'],
        },
        children: [
          {
            data: {
              label:
                'whether the annual reports disclosed the fulfilment of profit guarantee',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 306,
              _path: ['LRs', 'A28', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1050,
              index: 0,
              _parent: ['LRs', 'A28'],
            },
            children: [],
          },
          {
            data: {
              label: ' Shortfall of the profit guarantee',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 307,
              _path: ['LRs', 'A28', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1051,
              index: 0,
              _parent: ['LRs', 'A28'],
            },
            children: [],
          },
          {
            data: {
              label:
                '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 308,
              _path: ['LRs', 'A28', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1052,
              index: 0,
              _parent: ['LRs', 'A28'],
            },
            children: [],
          },
        ],
        childrenGroup: [
          [
            {
              data: {
                label:
                  'whether the annual reports disclosed the fulfilment of profit guarantee',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 306,
                _path: ['LRs', 'A28', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1050,
                index: 0,
                _parent: ['LRs', 'A28'],
              },
              children: [],
            },
            {
              data: {
                label: ' Shortfall of the profit guarantee',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 307,
                _path: ['LRs', 'A28', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1051,
                index: 0,
                _parent: ['LRs', 'A28'],
              },
              children: [],
            },
            {
              data: {
                label:
                  '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 308,
                _path: ['LRs', 'A28', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1052,
                index: 0,
                _parent: ['LRs', 'A28'],
              },
              children: [],
            },
          ],
        ],
      },
      {
        data: {
          label: 'A29',
          type: 'A29',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'A29'],
          _index: 263,
          _partType: 'top',
          _type: { label: 'A29', type: 'group' },
          _isHide: false,
          _nodeIndex: 1053,
          index: 0,
          _parent: ['LRs'],
        },
        children: [
          {
            data: {
              label: 'A29.1',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 311,
              _path: ['LRs', 'A29', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1054,
              index: 0,
              _parent: ['LRs', 'A29'],
            },
            children: [],
          },
          {
            data: {
              label: 'A29.2',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 312,
              _path: ['LRs', 'A29', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1055,
              index: 0,
              _parent: ['LRs', 'A29'],
            },
            children: [],
          },
          {
            data: {
              label: 'A29.3',
              required: false,
              multi: true,
              type: 'D/NS/N',
              words: '',
            },
            meta: {
              _index: 313,
              _path: ['LRs', 'A29', 'D/NS/N'],
              _partType: 'normal.schema',
              _type: {
                label: 'D/NS/N',
                values: [
                  { isDefault: false, name: 'disclosure' },
                  { isDefault: false, name: 'negative statement' },
                  { isDefault: false, name: 'no disclosure' },
                ],
                type: 'enum',
              },
              _isHide: false,
              _nodeIndex: 1056,
              index: 0,
              _parent: ['LRs', 'A29'],
            },
            children: [],
          },
        ],
        childrenGroup: [
          [
            {
              data: {
                label: 'A29.1',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 311,
                _path: ['LRs', 'A29', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1054,
                index: 0,
                _parent: ['LRs', 'A29'],
              },
              children: [],
            },
            {
              data: {
                label: 'A29.2',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 312,
                _path: ['LRs', 'A29', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1055,
                index: 0,
                _parent: ['LRs', 'A29'],
              },
              children: [],
            },
            {
              data: {
                label: 'A29.3',
                required: false,
                multi: true,
                type: 'D/NS/N',
                words: '',
              },
              meta: {
                _index: 313,
                _path: ['LRs', 'A29', 'D/NS/N'],
                _partType: 'normal.schema',
                _type: {
                  label: 'D/NS/N',
                  values: [
                    { isDefault: false, name: 'disclosure' },
                    { isDefault: false, name: 'negative statement' },
                    { isDefault: false, name: 'no disclosure' },
                  ],
                  type: 'enum',
                },
                _isHide: false,
                _nodeIndex: 1056,
                index: 0,
                _parent: ['LRs', 'A29'],
              },
              children: [],
            },
          ],
        ],
      },
      {
        data: {
          label: 'A30',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 264,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1057,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A31',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 265,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1058,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A32',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 266,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1059,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
      {
        data: {
          label: 'A33',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 267,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1060,
          index: 0,
          _parent: ['LRs'],
        },
        children: [],
      },
    ],
  },
];
