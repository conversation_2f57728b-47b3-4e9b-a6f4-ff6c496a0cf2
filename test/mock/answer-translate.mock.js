export const schema = {
  schemas: [
    {
      name: 'LRs',
      orders: [
        'A1',
        '(A1)',
        'A2',
        '(A2)',
        'A3',
        '(A3)',
        'A4',
        '(A4)',
        'A5',
        '(A5)',
        'A6',
        '(A6)',
        'A7',
        '(A7)',
        'A8',
        '(A8)',
        'A9',
        '(A9)',
        'A10',
        'A11',
        'A12',
        'A13',
        '(A13)',
        'A14',
        '(A14)',
        'A15',
        '(A15)',
        'A16',
        '(A16)',
        'A17',
        '(A17)',
        'A18',
        '(A18)',
        'A19',
        'A20',
        '(A20)',
        'A21',
        'A22',
        '(A22)',
        'A23',
        '(A23)',
        'A24',
        'A25',
        '(A25)',
        'A26',
        '(A26)',
        'A27',
        '(A27)',
        'A28',
        'A29',
        'A30',
        '(A30)',
        'A31',
        '(A31)',
        'A32',
        '(A32)',
        'A33',
        '(A33)',
      ],
      schema: {
        A1: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A1',
          _index: 3,
        },
        '(A1)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A1)',
          _index: 4,
        },
        A2: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A2 description',
          name: 'A2',
          _index: 5,
        },
        '(A2)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A2)',
          _index: 6,
        },
        A3: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A3 description',
          name: 'A3',
          _index: 7,
        },
        '(A3)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A3)',
          _index: 8,
        },
        A4: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A4 description',
          name: 'A4',
          _index: 9,
        },
        '(A4)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A4)',
          _index: 10,
        },
        A5: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A5描述信息',
          name: 'A5',
          _index: 11,
        },
        '(A5)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A5)',
          _index: 12,
        },
        A6: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A6',
          _index: 13,
        },
        '(A6)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A6)',
          _index: 14,
        },
        A7: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A7',
          _index: 15,
        },
        '(A7)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A7)',
          _index: 16,
        },
        A8: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A8',
          _index: 17,
        },
        '(A8)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A8)',
          _index: 18,
        },
        A9: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A9',
          _index: 19,
        },
        '(A9)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A9)',
          _index: 20,
        },
        A10: {
          type: 'A10',
          required: false,
          multi: true,
          words: 'A10 description',
          name: 'A10',
          _index: 21,
        },
        A11: {
          type: 'A11',
          required: false,
          multi: true,
          name: 'A11',
          _index: 22,
        },
        A12: {
          type: 'A12',
          required: false,
          multi: true,
          name: 'A12',
          _index: 23,
        },
        A13: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A13',
          _index: 24,
        },
        '(A13)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A13)',
          _index: 25,
        },
        A14: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A14',
          _index: 26,
        },
        '(A14)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A14)',
          _index: 27,
        },
        A15: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A15',
          _index: 28,
        },
        '(A15)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A15)',
          _index: 29,
        },
        A16: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A16',
          _index: 30,
        },
        '(A16)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A16)',
          _index: 31,
        },
        A17: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A17',
          _index: 32,
        },
        '(A17)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A17)',
          _index: 33,
        },
        A18: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A18',
          _index: 34,
        },
        '(A18)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A18)',
          _index: 35,
        },
        A19: {
          type: 'A19',
          required: false,
          multi: true,
          name: 'A19',
          _index: 36,
        },
        A20: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A20',
          _index: 37,
        },
        '(A20)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A20)',
          _index: 38,
        },
        A21: {
          type: 'A21',
          required: false,
          multi: true,
          name: 'A21',
          _index: 39,
        },
        A22: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A22',
          _index: 40,
        },
        '(A22)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A22)',
          _index: 41,
        },
        A23: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A23',
          _index: 42,
        },
        '(A23)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A23)',
          _index: 43,
        },
        A24: {
          type: 'A24',
          required: false,
          multi: true,
          name: 'A24',
          _index: 44,
        },
        A25: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A25',
          _index: 45,
        },
        '(A25)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A25)',
          _index: 46,
        },
        A26: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A26',
          _index: 47,
        },
        '(A26)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A26)',
          _index: 48,
        },
        A27: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A27',
          _index: 49,
        },
        '(A27)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A27)',
          _index: 50,
        },
        A28: {
          type: 'A28',
          required: false,
          multi: true,
          name: 'A28',
          _index: 51,
        },
        A29: {
          type: 'A29',
          required: false,
          multi: true,
          name: 'A29',
          _index: 52,
        },
        A30: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A30',
          _index: 53,
        },
        '(A30)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A30)',
          _index: 54,
        },
        A31: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A31',
          _index: 55,
        },
        '(A31)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A31)',
          _index: 56,
        },
        A32: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A32',
          _index: 57,
        },
        '(A32)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A32)',
          _index: 58,
        },
        A33: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A33',
          _index: 59,
        },
        '(A33)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A33)',
          _index: 60,
        },
      },
    },
    {
      name: 'A6',
      orders: ['A6', 'CONDITION'],
      schema: {
        A6: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A6',
          _index: 62,
        },
        CONDITION: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 63,
        },
      },
    },
    {
      name: 'A7',
      orders: ['A7', 'CONDITION'],
      schema: {
        A7: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A7',
          _index: 65,
        },
        CONDITION: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 66,
        },
      },
    },
    {
      name: 'A9',
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.1': {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'A9.1',
          _index: 68,
        },
        'A9.2': {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'A9.2',
          _index: 69,
        },
      },
    },
    {
      name: 'A10',
      orders: [
        'Name of every subsidiary',
        '(Name of every subsidiary)',
        'Country of operation',
        '(Country of operation)',
        'Country of incorporation',
        '(Country of incorporation)',
        ' If incorporated in the PRC, the kind of legal entity',
        '( If incorporated in the PRC, the kind of legal entity)',
        'Particulars of the issued share capital',
        '(Particulars of the issued share capital)',
        'Debt securities of every subsidiary',
        '(Debt securities of every subsidiary)',
      ],
      schema: {
        'Name of every subsidiary': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'name of every sub discription',
          name: 'Name of every subsidiary',
          _index: 71,
        },
        '(Name of every subsidiary)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(Name of every subsidiary)',
          _index: 72,
        },
        'Country of operation': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'country of operation description',
          name: 'Country of operation',
          _index: 73,
        },
        '(Country of operation)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(Country of operation)',
          _index: 74,
        },
        'Country of incorporation': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Country of incorporation',
          _index: 75,
        },
        '(Country of incorporation)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(Country of incorporation)',
          _index: 76,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 77,
        },
        '( If incorporated in the PRC, the kind of legal entity)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '( If incorporated in the PRC, the kind of legal entity)',
          _index: 78,
        },
        'Particulars of the issued share capital': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 79,
        },
        '(Particulars of the issued share capital)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(Particulars of the issued share capital)',
          _index: 80,
        },
        'Debt securities of every subsidiary': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'debt securities of every subsidiary',
          name: 'Debt securities of every subsidiary',
          _index: 81,
        },
        '(Debt securities of every subsidiary)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(Debt securities of every subsidiary)',
          _index: 82,
        },
      },
    },
    {
      name: 'A12',
      orders: [
        'List of directors',
        '(List of directors)',
        'Biography',
        '(Biography)',
      ],
      schema: {
        'List of directors': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'List of directors',
          _index: 84,
        },
        '(List of directors)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(List of directors)',
          _index: 85,
        },
        Biography: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Biography',
          _index: 86,
        },
        '(Biography)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(Biography)',
          _index: 87,
        },
      },
    },
    {
      name: 'A19',
      orders: ['A19.1', '(A19.1)', 'A19.2', '(A19.2)'],
      schema: {
        'A19.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A19.1',
          _index: 89,
        },
        '(A19.1)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A19.1)',
          _index: 90,
        },
        'A19.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A19.2',
          _index: 91,
        },
        '(A19.2)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A19.2)',
          _index: 92,
        },
      },
    },
    {
      name: 'A23',
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 94,
        },
        A23: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A23',
          _index: 95,
        },
      },
    },
    {
      name: 'A24',
      orders: [
        'Table for related party transaction',
        '(Table for related party transaction)',
        'Details',
        '(Details)',
      ],
      schema: {
        'Table for related party transaction': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Table for related party transaction',
          _index: 97,
        },
        '(Table for related party transaction)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(Table for related party transaction)',
          _index: 98,
        },
        Details: {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'Details',
          _index: 99,
        },
        '(Details)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(Details)',
          _index: 100,
        },
      },
    },
    {
      name: 'A25',
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 102,
        },
        A25: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A25',
          _index: 103,
        },
      },
    },
    {
      name: 'A26',
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 105,
        },
        A26: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A26',
          _index: 106,
        },
      },
    },
    {
      name: 'A27',
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          type: 'Y/N',
          required: false,
          multi: true,
          name: 'CONDITION',
          _index: 108,
        },
        A27: {
          type: 'Y/N/NA',
          required: false,
          multi: true,
          name: 'A27',
          _index: 109,
        },
      },
    },
    {
      name: 'A28',
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        '(whether the annual reports disclosed the fulfilment of profit guarantee)',
        ' Shortfall of the profit guarantee',
        '( Shortfall of the profit guarantee)',
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
        '(  disclosure of whether the counterparty compensates the shortfall of profit guarantee)',
      ],
      schema: {
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 111,
        },
        '(whether the annual reports disclosed the fulfilment of profit guarantee)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name:
            '(whether the annual reports disclosed the fulfilment of profit guarantee)',
          _index: 112,
        },
        ' Shortfall of the profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 113,
        },
        '( Shortfall of the profit guarantee)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '( Shortfall of the profit guarantee)',
          _index: 114,
        },
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 115,
        },
        '(  disclosure of whether the counterparty compensates the shortfall of profit guarantee)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name:
            '(  disclosure of whether the counterparty compensates the shortfall of profit guarantee)',
          _index: 116,
        },
      },
    },
    { name: 'text', orders: [], schema: {} },
    {
      name: 'A29',
      orders: ['A29.1', '(A29.1)', 'A29.2', '(A29.2)', 'A29.3', '(A29.3)'],
      schema: {
        'A29.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.1',
          _index: 119,
        },
        '(A29.1)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A29.1)',
          _index: 120,
        },
        'A29.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.2',
          _index: 121,
        },
        '(A29.2)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A29.2)',
          _index: 122,
        },
        'A29.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A29.3',
          _index: 123,
        },
        '(A29.3)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A29.3)',
          _index: 124,
        },
      },
    },
    {
      name: 'A11',
      orders: ['A11.1', '(A11.1)', 'A11.2', '(A11.2)', 'A11.3', '(A11.3)'],
      schema: {
        'A11.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.1',
          _index: 126,
        },
        '(A11.1)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A11.1)',
          _index: 127,
        },
        'A11.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.2',
          _index: 128,
        },
        '(A11.2)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A11.2)',
          _index: 129,
        },
        'A11.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A11.3',
          _index: 130,
        },
        '(A11.3)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A11.3)',
          _index: 131,
        },
      },
    },
    {
      name: 'A21',
      orders: [
        'A21.1',
        '(A21.1)',
        'A21.2',
        '(A21.2)',
        'A21.3',
        '(A21.3)',
        'A21.4',
        '(A21.4)',
        'A21.5',
        '(A21.5)',
      ],
      schema: {
        'A21.1': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.1',
          _index: 133,
        },
        '(A21.1)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A21.1)',
          _index: 134,
        },
        'A21.2': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.2',
          _index: 135,
        },
        '(A21.2)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A21.2)',
          _index: 136,
        },
        'A21.3': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.3',
          _index: 137,
        },
        '(A21.3)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A21.3)',
          _index: 138,
        },
        'A21.4': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.4',
          _index: 139,
        },
        '(A21.4)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A21.4)',
          _index: 140,
        },
        'A21.5': {
          type: 'D/NS/N',
          required: false,
          multi: true,
          name: 'A21.5',
          _index: 141,
        },
        '(A21.5)': {
          type: 'compliance assessment',
          required: false,
          multi: true,
          name: '(A21.5)',
          _index: 142,
        },
      },
    },
  ],
  schema_types: [
    {
      label: 'Y/N',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
      ],
      type: 'enum',
    },
    {
      label: 'Y/N/NA',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        {
          isDefault: false,
          name: 'N/A',
        },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N/In',
      values: [
        { isDefault: false, name: 'disclosure' },
        {
          isDefault: false,
          name: 'negative statement',
        },
        { isDefault: false, name: 'no disclosure' },
        { isDefault: false, name: 'Insufficient' },
      ],
      type: 'enum',
    },
    {
      label: 'P/F',
      values: [
        { isDefault: false, name: 'pass' },
        { isDefault: false, name: 'fail' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N',
      values: [
        { isDefault: false, name: 'disclosure' },
        {
          isDefault: false,
          name: 'negative statement',
        },
        { isDefault: false, name: 'no disclosure' },
      ],
      type: 'enum',
    },
    {
      label: 'compliance assessment',
      values: [
        { name: 'compliance', isDefault: false },
        {
          name: 'potential non-compliance',
          isDefault: false,
        },
      ],
      type: 'enum',
    },
  ],
  version: 'b90416367eca5e8c675fa6caea0e95d2',
};

export const answerV1 = {
  '4d0908fbffb3175d374b36f98f4cb1a2': {
    type: 'LRs',
    label: 'LRs',
    schemaPath: ['LRs'],
    md5: '4d0908fbffb3175d374b36f98f4cb1a2',
    attributes: [],
    items: [],
  },
  '55622a7fe414dbb873986be9ca331fe0': {
    type: 'D/NS/N',
    label: 'A1',
    schemaPath: ['LRs', 'A1'],
    md5: '55622a7fe414dbb873986be9ca331fe0',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A1', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [],
        schemaMD5: '55622a7fe414dbb873986be9ca331fe0',
        enumLabel: 'disclosure',
      },
      {
        schemaMD5: '55622a7fe414dbb873986be9ca331fe0',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '155.1521',
                  top: '397.7536',
                  topleft: ['397.7536', '155.1521'],
                  width: '304.6624',
                  height: '32.4409',
                  type: 'A1',
                  id: 'page1:1545096823000',
                  page: 0,
                },
                text: '首次公开发行股票并在创业板上市',
              },
            ],
            name: 'A1',
            label: '首次公开发行股票并在创业板上市',
          },
        ],
      },
      {
        schemaMD5: '55622a7fe414dbb873986be9ca331fe0',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '248.2434',
                  top: '440.0679',
                  topleft: ['440.0679', '248.2434'],
                  width: '102.9646',
                  height: '20.4519',
                  type: 'A1',
                  id: 'page1:1545096828000',
                  page: 0,
                },
                text: '招股说明书',
              },
            ],
            name: 'A1',
            label: '招股说明书',
          },
        ],
      },
    ],
  },
  cbd1ff3155f59ccf023a8788935871e9: {
    type: 'compliance assessment',
    label: '(A1)',
    schemaPath: ['LRs', '(A1)'],
    md5: 'cbd1ff3155f59ccf023a8788935871e9',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A1)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  cbd73d3b857ae4d6907ec37b4502b6df: {
    type: 'D/NS/N',
    label: 'A2',
    schemaPath: ['LRs', 'A2'],
    md5: 'cbd73d3b857ae4d6907ec37b4502b6df',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A2', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [],
        schemaMD5: 'cbd73d3b857ae4d6907ec37b4502b6df',
        enumLabel: 'negative statement',
      },
      {
        schemaMD5: 'cbd73d3b857ae4d6907ec37b4502b6df',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '243.3068',
                  top: '68.4080',
                  topleft: ['68.4080', '243.3068'],
                  width: '35.9671',
                  height: '24.6833',
                  type: 'A2',
                  id: 'page2:1545096838000',
                  page: 1,
                },
                text: '本次',
              },
              {
                frameData: {
                  left: '311.7147',
                  top: '66.9975',
                  topleft: ['66.9975', '311.7147'],
                  width: '38.0828',
                  height: '28.9147',
                  type: 'A2',
                  id: 'page2:1545096840000',
                  page: 1,
                },
                text: '概况',
              },
            ],
            name: 'A2',
            label: '本次概况',
          },
        ],
      },
      {
        schemaMD5: 'cbd73d3b857ae4d6907ec37b4502b6df',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '312.4200',
                  top: '191.8245',
                  topleft: ['191.8245', '312.4200'],
                  width: '90.9756',
                  height: '12.6943',
                  type: 'A2',
                  id: 'page2:1545096849000',
                  page: 1,
                },
                text: '深圳证券交易所',
              },
            ],
            name: 'A2',
            label: '深圳证券交易所',
          },
        ],
      },
    ],
  },
  '4a3acd534ab638fc2f733bdd7a638d04': {
    type: 'compliance assessment',
    label: '(A2)',
    schemaPath: ['LRs', '(A2)'],
    md5: '4a3acd534ab638fc2f733bdd7a638d04',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A2)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '4fdd42289a3935ebf0dce1859102466d': {
    type: 'D/NS/N',
    label: 'A3',
    schemaPath: ['LRs', 'A3'],
    md5: '4fdd42289a3935ebf0dce1859102466d',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A3', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [],
        schemaMD5: '4fdd42289a3935ebf0dce1859102466d',
        enumLabel: 'no disclosure',
      },
      {
        schemaMD5: '4fdd42289a3935ebf0dce1859102466d',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '86.7441',
                  top: '193.2349',
                  topleft: ['193.2349', '86.7441'],
                  width: '98.0279',
                  height: '16.2205',
                  type: 'A3',
                  id: 'page2:1545096861000',
                  page: 1,
                },
                text: '拟上市的证券交易所',
              },
            ],
            name: 'A3',
            label: '拟上市的证券交易所',
          },
        ],
      },
    ],
  },
  b56ae35594583d974563c5afcb9eedad: {
    type: 'compliance assessment',
    label: '(A3)',
    schemaPath: ['LRs', '(A3)'],
    md5: 'b56ae35594583d974563c5afcb9eedad',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A3)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '852ddfd43c27f4041daf0d6343401b56': {
    type: 'D/NS/N',
    label: 'A4',
    schemaPath: ['LRs', 'A4'],
    md5: '852ddfd43c27f4041daf0d6343401b56',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A4', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [],
        schemaMD5: '852ddfd43c27f4041daf0d6343401b56',
        enumLabel: 'no disclosure',
      },
    ],
  },
  '41bc2d58cd2beb400271fc51916bd787': {
    type: 'compliance assessment',
    label: '(A4)',
    schemaPath: ['LRs', '(A4)'],
    md5: '41bc2d58cd2beb400271fc51916bd787',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A4)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  f6c1d5cbd96bfee2501b32415a564dda: {
    type: 'D/NS/N',
    label: 'A5',
    schemaPath: ['LRs', 'A5'],
    md5: 'f6c1d5cbd96bfee2501b32415a564dda',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A5', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        fields: [],
        schemaMD5: 'f6c1d5cbd96bfee2501b32415a564dda',
        enumLabel: 'disclosure',
      },
    ],
  },
  '0ea54058d69c1a30767906763621d07d': {
    type: 'compliance assessment',
    label: '(A5)',
    schemaPath: ['LRs', '(A5)'],
    md5: '0ea54058d69c1a30767906763621d07d',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A5)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  fb154e94b84d507b40ddf6bd137937dc: {
    type: 'D/NS/N',
    label: 'A6',
    schemaPath: ['LRs', 'A6'],
    md5: 'fb154e94b84d507b40ddf6bd137937dc',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A6', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [
      {
        schemaMD5: 'fb154e94b84d507b40ddf6bd137937dc',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '288.4419',
                  top: '168.5516',
                  topleft: ['168.5516', '288.4419'],
                  width: '157.2678',
                  height: '17.6309',
                  type: 'A6',
                  id: 'page2:1545097126000',
                  page: 1,
                },
                text: '长江证券承销保荐有限公司',
              },
            ],
            name: 'A6',
            label: '长江证券承销保荐有限公司',
          },
        ],
      },
      {
        schemaMD5: 'fb154e94b84d507b40ddf6bd137937dc',
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '308.8938',
                  top: '191.8245',
                  topleft: ['191.8245', '308.8938'],
                  width: '96.6175',
                  height: '11.9890',
                  type: 'A6',
                  id: 'page2:1545097128000',
                  page: 1,
                },
                text: '深圳证券交易所',
              },
            ],
            name: 'A6',
            label: '深圳证券交易所',
          },
        ],
      },
    ],
  },
  '7f1de1096c9a312a2a1ac6375e0668a6': {
    type: 'compliance assessment',
    label: '(A6)',
    schemaPath: ['LRs', '(A6)'],
    md5: '7f1de1096c9a312a2a1ac6375e0668a6',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A6)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '14f853960458d8582fa87d245c0ca158': {
    type: 'D/NS/N',
    label: 'A7',
    schemaPath: ['LRs', 'A7'],
    md5: '14f853960458d8582fa87d245c0ca158',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A7', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '9a0138e4c99ba3249c02c1a0f6d8f23d': {
    type: 'compliance assessment',
    label: '(A7)',
    schemaPath: ['LRs', '(A7)'],
    md5: '9a0138e4c99ba3249c02c1a0f6d8f23d',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A7)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '1c2abe3ba78ef57dbd6437941e28b9a0': {
    type: 'D/NS/N',
    label: 'A8',
    schemaPath: ['LRs', 'A8'],
    md5: '1c2abe3ba78ef57dbd6437941e28b9a0',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A8', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '772e732efd5d01a85aec024eb89c3720': {
    type: 'compliance assessment',
    label: '(A8)',
    schemaPath: ['LRs', '(A8)'],
    md5: '772e732efd5d01a85aec024eb89c3720',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A8)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '9de1725bc2bf09e94ccdef80d87e5bda': {
    type: 'D/NS/N',
    label: 'A9',
    schemaPath: ['LRs', 'A9'],
    md5: '9de1725bc2bf09e94ccdef80d87e5bda',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A9', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  bfd8246d3cf75782a9c71bb94bb5ec0f: {
    type: 'compliance assessment',
    label: '(A9)',
    schemaPath: ['LRs', '(A9)'],
    md5: 'bfd8246d3cf75782a9c71bb94bb5ec0f',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A9)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  ee6df835798bd6f92eb956f51b401800: {
    type: 'A10',
    label: 'A10',
    schemaPath: ['LRs', 'A10'],
    md5: 'ee6df835798bd6f92eb956f51b401800',
    required: false,
    multiple: true,
    attributes: [
      {
        name: 'Name of every subsidiary',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: '(Name of every subsidiary)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      {
        name: 'Country of operation',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: '(Country of operation)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      {
        name: 'Country of incorporation',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: '(Country of incorporation)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      {
        name: ' If incorporated in the PRC, the kind of legal entity',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: '( If incorporated in the PRC, the kind of legal entity)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      {
        name: 'Particulars of the issued share capital',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: '(Particulars of the issued share capital)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      {
        name: 'Debt securities of every subsidiary',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: '(Debt securities of every subsidiary)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [
      {
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '86.7441',
                  top: '105.7855',
                  topleft: ['105.7855', '86.7441'],
                  width: '75.4604',
                  height: '19.0414',
                  type: 'Name of every subsidiary',
                  id: 'page2:1545096879000',
                  page: 1,
                },
                text: '发行股票类型',
              },
              {
                frameData: {
                  left: '411.1532',
                  top: '105.7855',
                  topleft: ['105.7855', '411.1532'],
                  width: '89.5651',
                  height: '17.6309',
                  type: 'Name of every subsidiary',
                  id: 'page2:1545096885000',
                  page: 1,
                },
                text: '不超过 2,668 万股',
              },
            ],
            label: '发行股票类型|_|_|不超过 2,668 万股',
            name: 'Name of every subsidiary',
            type: 'D/NS/N',
            enumLabel: 'disclosure',
          },
          {
            components: [],
            label: '',
            name: '(Name of every subsidiary)',
            type: 'compliance assessment',
          },
          {
            components: [
              {
                frameData: {
                  left: '287.0314',
                  top: '168.5516',
                  topleft: ['168.5516', '287.0314'],
                  width: '28.2095',
                  height: '18.3362',
                  type: 'Country of operation',
                  id: 'page2:1545096893000',
                  page: 1,
                },
                text: '长江',
              },
              {
                frameData: {
                  left: '359.6709',
                  top: '190.4140',
                  topleft: ['190.4140', '359.6709'],
                  width: '42.3142',
                  height: '18.3362',
                  type: 'Country of operation',
                  id: 'page2:1545096896000',
                  page: 1,
                },
                text: '交易所',
              },
              {
                frameData: {
                  left: '329.3457',
                  top: '126.9427',
                  topleft: ['126.9427', '329.3457'],
                  width: '63.4713',
                  height: '15.5152',
                  type: 'Country of operation',
                  id: 'page2:1545096908000',
                  page: 1,
                },
                text: '每股发行价格',
              },
            ],
            label: '长江交易所|_|_|每股发行价格',
            name: 'Country of operation',
            type: 'D/NS/N',
            enumLabel: 'negative statement',
          },
          {
            components: [],
            label: '',
            name: '(Country of operation)',
            type: 'compliance assessment',
          },
          {
            components: [
              {
                frameData: {
                  left: '86.7441',
                  top: '212.9816',
                  topleft: ['212.9816', '86.7441'],
                  width: '100.1436',
                  height: '12.6943',
                  type: 'Country of incorporation',
                  id: 'page2:1545096920000',
                  page: 1,
                },
                text: '招股说明书签署日期',
              },
              {
                frameData: {
                  left: '86.7441',
                  top: '212.9816',
                  topleft: ['212.9816', '86.7441'],
                  width: '100.1436',
                  height: '12.6943',
                  type: 'Country of incorporation',
                  id: 'page2:1545096920000',
                  page: 1,
                },
                text: '招股说明书签署日期',
              },
              {
                frameData: {
                  left: '116.3641',
                  top: '114.2484',
                  topleft: ['114.2484', '116.3641'],
                  width: '45.8404',
                  height: '22.5676',
                  type: 'Country of incorporation',
                  id: 'page4:1545096947000',
                  page: 3,
                },
                text: '发行人',
              },
              {
                frameData: {
                  left: '428.7841',
                  top: '112.1327',
                  topleft: ['112.1327', '428.7841'],
                  width: '44.4299',
                  height: '23.9781',
                  type: 'Country of incorporation',
                  id: 'page4:1545096950000',
                  page: 3,
                },
                text: '说明书',
              },
            ],
            label: '招股说明书签署日期|_|_|招股说明书签署日期发行人说明书',
            name: 'Country of incorporation',
            type: 'D/NS/N',
            enumLabel: 'no disclosure',
          },
          {
            components: [],
            label: '',
            name: '(Country of incorporation)',
            type: 'compliance assessment',
          },
          {
            components: [],
            label: '',
            name: ' If incorporated in the PRC, the kind of legal entity',
            type: 'D/NS/N',
            enumLabel: 'negative statement',
          },
          {
            components: [],
            label: '',
            name: '( If incorporated in the PRC, the kind of legal entity)',
            type: 'compliance assessment',
          },
          {
            components: [],
            label: '',
            name: 'Particulars of the issued share capital',
            type: 'D/NS/N',
            enumLabel: 'disclosure',
          },
          {
            components: [],
            label: '',
            name: '(Particulars of the issued share capital)',
            type: 'compliance assessment',
          },
          {
            components: [],
            label: '',
            name: 'Debt securities of every subsidiary',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: '(Debt securities of every subsidiary)',
            type: 'compliance assessment',
          },
        ],
        schemaMD5: 'ee6df835798bd6f92eb956f51b401800',
      },
      {
        fields: [
          {
            components: [
              {
                frameData: {
                  left: '116.3641',
                  top: '331.4614',
                  topleft: ['331.4614', '116.3641'],
                  width: '76.8708',
                  height: '21.8623',
                  type: 'Name of every subsidiary',
                  id: 'page4:1545096966000',
                  page: 3,
                },
                text: '保荐人承诺',
              },
            ],
            label: '保荐人承诺',
            name: 'Name of every subsidiary',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: '(Name of every subsidiary)',
            type: 'compliance assessment',
          },
          {
            components: [
              {
                frameData: {
                  left: '189.7087',
                  top: '387.1751',
                  topleft: ['387.1751', '189.7087'],
                  width: '26.7990',
                  height: '23.9781',
                  type: 'Country of operation',
                  id: 'page4:1545096981000',
                  page: 3,
                },
                text: '损失',
              },
              {
                frameData: {
                  left: '443.5941',
                  top: '409.7427',
                  topleft: ['409.7427', '443.5941'],
                  width: '29.6200',
                  height: '26.7990',
                  type: 'Country of operation',
                  id: 'page4:1545096983000',
                  page: 3,
                },
                text: '出具',
              },
              {
                frameData: {
                  left: '288.4419',
                  top: '165.7307',
                  topleft: ['165.7307', '288.4419'],
                  width: '55.7137',
                  height: '23.9781',
                  type: 'Country of operation',
                  id: 'page4:1545097009000',
                  page: 3,
                },
                text: '法律责任',
              },
              {
                frameData: {
                  left: '456.2883',
                  top: '198.1716',
                  topleft: ['198.1716', '456.2883'],
                  width: '43.7247',
                  height: '23.2728',
                  type: 'Country of operation',
                  id: 'page4:1545097013000',
                  page: 3,
                },
                text: '控股股',
              },
            ],
            label: '损失出具|_|_|法律责任控股股',
            name: 'Country of operation',
            type: 'D/NS/N',
            enumLabel: 'no disclosure',
          },
          {
            components: [],
            label: '',
            name: '(Country of operation)',
            type: 'compliance assessment',
          },
          {
            components: [
              {
                frameData: {
                  left: '424.5527',
                  top: '327.2300',
                  topleft: ['327.2300', '424.5527'],
                  width: '74.7551',
                  height: '28.2095',
                  type: 'Country of incorporation',
                  id: 'page4:1545097000000',
                  page: 3,
                },
                text: '出具的文件',
              },
              {
                frameData: {
                  left: '358.9656',
                  top: '441.4783',
                  topleft: ['441.4783', '358.9656'],
                  width: '113.5432',
                  height: '22.5676',
                  type: 'Country of incorporation',
                  id: 'page4:1545097003000',
                  page: 3,
                },
                text: '给他人造成损失的',
              },
            ],
            label: '出具的文件|_|_|给他人造成损失的',
            name: 'Country of incorporation',
            type: 'D/NS/N',
            enumLabel: 'negative statement',
          },
          {
            components: [],
            label: '',
            name: '(Country of incorporation)',
            type: 'compliance assessment',
          },
          {
            components: [],
            label: '',
            name: ' If incorporated in the PRC, the kind of legal entity',
            type: 'D/NS/N',
            enumLabel: 'disclosure',
          },
          {
            components: [],
            label: '',
            name: '( If incorporated in the PRC, the kind of legal entity)',
            type: 'compliance assessment',
          },
          {
            components: [],
            label: '',
            name: 'Particulars of the issued share capital',
            type: 'D/NS/N',
            enumLabel: 'negative statement',
          },
          {
            components: [],
            label: '',
            name: '(Particulars of the issued share capital)',
            type: 'compliance assessment',
          },
          {
            components: [],
            label: '',
            name: 'Debt securities of every subsidiary',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: '(Debt securities of every subsidiary)',
            type: 'compliance assessment',
          },
        ],
        schemaMD5: 'ee6df835798bd6f92eb956f51b401800',
      },
    ],
  },
  '0dac6231926962eacebc061b8e26c2ea': {
    type: 'A11',
    label: 'A11',
    schemaPath: ['LRs', 'A11'],
    md5: '0dac6231926962eacebc061b8e26c2ea',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A11.1', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A11.1)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'A11.2', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A11.2)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'A11.3', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A11.3)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A11.1', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A11.1)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'A11.2', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A11.2)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'A11.3', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A11.3)',
            type: 'compliance assessment',
          },
        ],
        schemaMD5: '0dac6231926962eacebc061b8e26c2ea',
      },
    ],
  },
  '5f1a10b42e2c2aee40c8fae2223540a0': {
    type: 'A12',
    label: 'A12',
    schemaPath: ['LRs', 'A12'],
    md5: '5f1a10b42e2c2aee40c8fae2223540a0',
    required: false,
    multiple: true,
    attributes: [
      {
        name: 'List of directors',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: '(List of directors)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'Biography', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(Biography)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name: 'List of directors',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: '(List of directors)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'Biography', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(Biography)',
            type: 'compliance assessment',
          },
        ],
        schemaMD5: '5f1a10b42e2c2aee40c8fae2223540a0',
      },
    ],
  },
  '276a3530e4308ad6f36dfc7810269a40': {
    type: 'D/NS/N',
    label: 'A13',
    schemaPath: ['LRs', 'A13'],
    md5: '276a3530e4308ad6f36dfc7810269a40',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A13', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '8b33c7157fe5c386e366cb3cb0760dbc': {
    type: 'compliance assessment',
    label: '(A13)',
    schemaPath: ['LRs', '(A13)'],
    md5: '8b33c7157fe5c386e366cb3cb0760dbc',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A13)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  b7bcead16024b3ee5fba6f1091b2389f: {
    type: 'D/NS/N',
    label: 'A14',
    schemaPath: ['LRs', 'A14'],
    md5: 'b7bcead16024b3ee5fba6f1091b2389f',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A14', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '316cf948c2f19559d19b17807015d6d1': {
    type: 'compliance assessment',
    label: '(A14)',
    schemaPath: ['LRs', '(A14)'],
    md5: '316cf948c2f19559d19b17807015d6d1',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A14)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  a234547b9489581e03fed3db1f3fa0a5: {
    type: 'D/NS/N',
    label: 'A15',
    schemaPath: ['LRs', 'A15'],
    md5: 'a234547b9489581e03fed3db1f3fa0a5',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A15', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '977242784830e195197d135226feb185': {
    type: 'compliance assessment',
    label: '(A15)',
    schemaPath: ['LRs', '(A15)'],
    md5: '977242784830e195197d135226feb185',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A15)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  fbc22c13ad39f4d3c24dd2384400b9cc: {
    type: 'D/NS/N',
    label: 'A16',
    schemaPath: ['LRs', 'A16'],
    md5: 'fbc22c13ad39f4d3c24dd2384400b9cc',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A16', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  bcd0571aa12ce03766655c70318ddc57: {
    type: 'compliance assessment',
    label: '(A16)',
    schemaPath: ['LRs', '(A16)'],
    md5: 'bcd0571aa12ce03766655c70318ddc57',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A16)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '03b47234a87942e26ad5355a02dc85e9': {
    type: 'D/NS/N',
    label: 'A17',
    schemaPath: ['LRs', 'A17'],
    md5: '03b47234a87942e26ad5355a02dc85e9',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A17', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '4db6dd465ae9465407b6877102e387ff': {
    type: 'compliance assessment',
    label: '(A17)',
    schemaPath: ['LRs', '(A17)'],
    md5: '4db6dd465ae9465407b6877102e387ff',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A17)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  e405bd7c51785bee36cc42d638e8cc88: {
    type: 'D/NS/N',
    label: 'A18',
    schemaPath: ['LRs', 'A18'],
    md5: 'e405bd7c51785bee36cc42d638e8cc88',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A18', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '93e5575a7f8e0ee349e7f217f477a486': {
    type: 'compliance assessment',
    label: '(A18)',
    schemaPath: ['LRs', '(A18)'],
    md5: '93e5575a7f8e0ee349e7f217f477a486',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A18)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  b2b9c2e5ff83433b80cfcbbc0353dbdf: {
    type: 'A19',
    label: 'A19',
    schemaPath: ['LRs', 'A19'],
    md5: 'b2b9c2e5ff83433b80cfcbbc0353dbdf',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A19.1', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A19.1)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'A19.2', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A19.2)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A19.1', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A19.1)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'A19.2', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A19.2)',
            type: 'compliance assessment',
          },
        ],
        schemaMD5: 'b2b9c2e5ff83433b80cfcbbc0353dbdf',
      },
    ],
  },
  '48e7d13479b4795a1246fbfad1115763': {
    type: 'D/NS/N',
    label: 'A20',
    schemaPath: ['LRs', 'A20'],
    md5: '48e7d13479b4795a1246fbfad1115763',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A20', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '3f5e2f50f587447aa719a64b80136527': {
    type: 'compliance assessment',
    label: '(A20)',
    schemaPath: ['LRs', '(A20)'],
    md5: '3f5e2f50f587447aa719a64b80136527',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A20)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '74f8a83d40a5f2b5a592833ca1394045': {
    type: 'A21',
    label: 'A21',
    schemaPath: ['LRs', 'A21'],
    md5: '74f8a83d40a5f2b5a592833ca1394045',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A21.1', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A21.1)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'A21.2', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A21.2)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'A21.3', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A21.3)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'A21.4', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A21.4)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'A21.5', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A21.5)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A21.1', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A21.1)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'A21.2', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A21.2)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'A21.3', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A21.3)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'A21.4', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A21.4)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'A21.5', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A21.5)',
            type: 'compliance assessment',
          },
        ],
        schemaMD5: '74f8a83d40a5f2b5a592833ca1394045',
      },
    ],
  },
  '6e6ed22156a145885dc528ab2fd109bd': {
    type: 'D/NS/N',
    label: 'A22',
    schemaPath: ['LRs', 'A22'],
    md5: '6e6ed22156a145885dc528ab2fd109bd',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A22', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  acdff85edf20ef214ef9e3cb9638d9de: {
    type: 'compliance assessment',
    label: '(A22)',
    schemaPath: ['LRs', '(A22)'],
    md5: 'acdff85edf20ef214ef9e3cb9638d9de',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A22)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  a1fc0713d58412148c94c9522bd11af4: {
    type: 'D/NS/N',
    label: 'A23',
    schemaPath: ['LRs', 'A23'],
    md5: 'a1fc0713d58412148c94c9522bd11af4',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A23', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '548687cc651f4090631b1c67f7463a53': {
    type: 'compliance assessment',
    label: '(A23)',
    schemaPath: ['LRs', '(A23)'],
    md5: '548687cc651f4090631b1c67f7463a53',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A23)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  ce0c2bc50143e8e3f15ca93e19b26bcc: {
    type: 'A24',
    label: 'A24',
    schemaPath: ['LRs', 'A24'],
    md5: 'ce0c2bc50143e8e3f15ca93e19b26bcc',
    required: false,
    multiple: true,
    attributes: [
      {
        name: 'Table for related party transaction',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: '(Table for related party transaction)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'Details', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(Details)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name: 'Table for related party transaction',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: '(Table for related party transaction)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'Details', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(Details)',
            type: 'compliance assessment',
          },
        ],
        schemaMD5: 'ce0c2bc50143e8e3f15ca93e19b26bcc',
      },
    ],
  },
  '6cdbd2846df7034f6bf62f57dc117403': {
    type: 'D/NS/N',
    label: 'A25',
    schemaPath: ['LRs', 'A25'],
    md5: '6cdbd2846df7034f6bf62f57dc117403',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A25', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '67bc6be8a0fc8a8411eb5eaf6b153433': {
    type: 'compliance assessment',
    label: '(A25)',
    schemaPath: ['LRs', '(A25)'],
    md5: '67bc6be8a0fc8a8411eb5eaf6b153433',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A25)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '4f078c2d7aa5df5ab81fee975e777d7e': {
    type: 'D/NS/N',
    label: 'A26',
    schemaPath: ['LRs', 'A26'],
    md5: '4f078c2d7aa5df5ab81fee975e777d7e',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A26', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '2a31920b0f032870ebe04b88ea17eed6': {
    type: 'compliance assessment',
    label: '(A26)',
    schemaPath: ['LRs', '(A26)'],
    md5: '2a31920b0f032870ebe04b88ea17eed6',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A26)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '7cda96ae6f0d4c4b45aa412b39e81b05': {
    type: 'D/NS/N',
    label: 'A27',
    schemaPath: ['LRs', 'A27'],
    md5: '7cda96ae6f0d4c4b45aa412b39e81b05',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A27', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '47e1d85af472e210502485faa286485f': {
    type: 'compliance assessment',
    label: '(A27)',
    schemaPath: ['LRs', '(A27)'],
    md5: '47e1d85af472e210502485faa286485f',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A27)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '14876b40d5252b081bc9d341a8e3c67c': {
    type: 'A28',
    label: 'A28',
    schemaPath: ['LRs', 'A28'],
    md5: '14876b40d5252b081bc9d341a8e3c67c',
    required: false,
    multiple: true,
    attributes: [
      {
        name:
          'whether the annual reports disclosed the fulfilment of profit guarantee',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name:
          '(whether the annual reports disclosed the fulfilment of profit guarantee)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      {
        name: ' Shortfall of the profit guarantee',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name: '( Shortfall of the profit guarantee)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      {
        name:
          '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
        multiple: true,
        required: false,
        type: 'D/NS/N',
      },
      {
        name:
          '(  disclosure of whether the counterparty compensates the shortfall of profit guarantee)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [
      {
        fields: [
          {
            components: [],
            label: '',
            name:
              'whether the annual reports disclosed the fulfilment of profit guarantee',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name:
              '(whether the annual reports disclosed the fulfilment of profit guarantee)',
            type: 'compliance assessment',
          },
          {
            components: [],
            label: '',
            name: ' Shortfall of the profit guarantee',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name: '( Shortfall of the profit guarantee)',
            type: 'compliance assessment',
          },
          {
            components: [],
            label: '',
            name:
              '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
            type: 'D/NS/N',
          },
          {
            components: [],
            label: '',
            name:
              '(  disclosure of whether the counterparty compensates the shortfall of profit guarantee)',
            type: 'compliance assessment',
          },
        ],
        schemaMD5: '14876b40d5252b081bc9d341a8e3c67c',
      },
    ],
  },
  '5fed68f9a2bb9f6e73e724201186f377': {
    type: 'A29',
    label: 'A29',
    schemaPath: ['LRs', 'A29'],
    md5: '5fed68f9a2bb9f6e73e724201186f377',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A29.1', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A29.1)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'A29.2', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A29.2)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
      { name: 'A29.3', multiple: true, required: false, type: 'D/NS/N' },
      {
        name: '(A29.3)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [
      {
        fields: [
          { components: [], label: '', name: 'A29.1', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A29.1)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'A29.2', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A29.2)',
            type: 'compliance assessment',
          },
          { components: [], label: '', name: 'A29.3', type: 'D/NS/N' },
          {
            components: [],
            label: '',
            name: '(A29.3)',
            type: 'compliance assessment',
          },
        ],
        schemaMD5: '5fed68f9a2bb9f6e73e724201186f377',
      },
    ],
  },
  '653fbb1322d34df21db22237f8b24ffa': {
    type: 'D/NS/N',
    label: 'A30',
    schemaPath: ['LRs', 'A30'],
    md5: '653fbb1322d34df21db22237f8b24ffa',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A30', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '4a21ba19eb2765e6062b7cb5eb62f66f': {
    type: 'compliance assessment',
    label: '(A30)',
    schemaPath: ['LRs', '(A30)'],
    md5: '4a21ba19eb2765e6062b7cb5eb62f66f',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A30)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '57271076057cb476b14a8af6d75ed4ec': {
    type: 'D/NS/N',
    label: 'A31',
    schemaPath: ['LRs', 'A31'],
    md5: '57271076057cb476b14a8af6d75ed4ec',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A31', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '8badb533e64b3a4eff113be172d35d7b': {
    type: 'compliance assessment',
    label: '(A31)',
    schemaPath: ['LRs', '(A31)'],
    md5: '8badb533e64b3a4eff113be172d35d7b',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A31)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  '908786b89c9d422a3236ec0ad3abe1b3': {
    type: 'D/NS/N',
    label: 'A32',
    schemaPath: ['LRs', 'A32'],
    md5: '908786b89c9d422a3236ec0ad3abe1b3',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A32', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  d4c7049c291ad2e7bfb4a132ec197e01: {
    type: 'compliance assessment',
    label: '(A32)',
    schemaPath: ['LRs', '(A32)'],
    md5: 'd4c7049c291ad2e7bfb4a132ec197e01',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A32)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
  fe1f96e934742fcd3f8c35c1035c29b1: {
    type: 'D/NS/N',
    label: 'A33',
    schemaPath: ['LRs', 'A33'],
    md5: 'fe1f96e934742fcd3f8c35c1035c29b1',
    required: false,
    multiple: true,
    attributes: [
      { name: 'A33', multiple: true, required: false, type: 'D/NS/N' },
    ],
    items: [],
  },
  '934e0bed7afccf72bd81ec2104c50959': {
    type: 'compliance assessment',
    label: '(A33)',
    schemaPath: ['LRs', '(A33)'],
    md5: '934e0bed7afccf72bd81ec2104c50959',
    required: false,
    multiple: true,
    attributes: [
      {
        name: '(A33)',
        multiple: true,
        required: false,
        type: 'compliance assessment',
      },
    ],
    items: [],
  },
};

export const answerV2_2 = {
  version: '2.2',
  items: [
    {
      key: '["LRs:0","A1:0"]',
      schema: {
        data: {
          label: 'A1',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 155.1521,
                box_top: 397.7536,
                box_right: 459.81449999999995,
                box_bottom: 430.1945,
              },
              page: 0,
              text: '首次公开发行股票并在创业板上市',
            },
          ],
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 248.2434,
                box_top: 440.0679,
                box_right: 351.208,
                box_bottom: 460.51980000000003,
              },
              page: 0,
              text: '招股说明书',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: 'disclosure',
    },
    {
      key: '["LRs:0","A2:0"]',
      schema: {
        data: {
          label: 'A2',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A2 description',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 243.3068,
                box_top: 68.408,
                box_right: 279.2739,
                box_bottom: 93.0913,
              },
              page: 1,
              text: '本次',
            },
            {
              box: {
                box_left: 311.7147,
                box_top: 66.9975,
                box_right: 349.7975,
                box_bottom: 95.9122,
              },
              page: 1,
              text: '概况',
            },
          ],
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 312.42,
                box_top: 191.8245,
                box_right: 403.3956,
                box_bottom: 204.5188,
              },
              page: 1,
              text: '深圳证券交易所',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: 'negative statement',
    },
    {
      key: '["LRs:0","A3:0"]',
      schema: {
        data: {
          label: 'A3',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A3 description',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 86.7441,
                box_top: 193.2349,
                box_right: 184.772,
                box_bottom: 209.4554,
              },
              page: 1,
              text: '拟上市的证券交易所',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: 'no disclosure',
    },
    {
      key: '["LRs:0","A4:0"]',
      schema: {
        data: {
          label: 'A4',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A4 description',
        },
      },
      data: [],
      value: 'no disclosure',
    },
    {
      key: '["LRs:0","A5:0"]',
      schema: {
        data: {
          label: 'A5',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: 'A5描述信息',
        },
      },
      data: [],
      value: 'disclosure',
    },
    {
      key: '["LRs:0","A6:0"]',
      schema: {
        data: {
          label: 'A6',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 288.4419,
                box_top: 168.5516,
                box_right: 445.7097,
                box_bottom: 186.1825,
              },
              page: 1,
              text: '长江证券承销保荐有限公司',
            },
          ],
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 308.8938,
                box_top: 191.8245,
                box_right: 405.5113,
                box_bottom: 203.8135,
              },
              page: 1,
              text: '深圳证券交易所',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: null,
    },
    {
      key: '["LRs:0","A10:0","Name of every subsidiary:0"]',
      schema: {
        data: {
          label: 'Name of every subsidiary',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: 'name of every sub discription',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 86.7441,
                box_top: 105.7855,
                box_right: 162.2045,
                box_bottom: 124.8269,
              },
              page: 1,
              text: '发行股票类型',
            },
          ],
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 411.1532,
                box_top: 105.7855,
                box_right: 500.7183,
                box_bottom: 123.4164,
              },
              page: 1,
              text: '不超过 2,668 万股',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: 'disclosure',
    },
    {
      key: '["LRs:0","A10:0","Country of operation:0"]',
      schema: {
        data: {
          label: 'Country of operation',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: 'country of operation description',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 287.0314,
                box_top: 168.5516,
                box_right: 315.2409,
                box_bottom: 186.8878,
              },
              page: 1,
              text: '长江',
            },
            {
              box: {
                box_left: 359.6709,
                box_top: 190.414,
                box_right: 401.9851,
                box_bottom: 208.75019999999998,
              },
              page: 1,
              text: '交易所',
            },
          ],
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 329.3457,
                box_top: 126.9427,
                box_right: 392.817,
                box_bottom: 142.4579,
              },
              page: 1,
              text: '每股发行价格',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: 'negative statement',
    },
    {
      key: '["LRs:0","A10:0","Country of incorporation:0"]',
      schema: {
        data: {
          label: 'Country of incorporation',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: '',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 86.7441,
                box_top: 212.9816,
                box_right: 186.8877,
                box_bottom: 225.67589999999998,
              },
              page: 1,
              text: '招股说明书签署日期',
            },
          ],
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 86.7441,
                box_top: 212.9816,
                box_right: 186.8877,
                box_bottom: 225.67589999999998,
              },
              page: 1,
              text: '招股说明书签署日期',
            },
            {
              box: {
                box_left: 116.3641,
                box_top: 114.2484,
                box_right: 162.2045,
                box_bottom: 136.816,
              },
              page: 3,
              text: '发行人',
            },
            {
              box: {
                box_left: 428.7841,
                box_top: 112.1327,
                box_right: 473.21400000000006,
                box_bottom: 136.1108,
              },
              page: 3,
              text: '说明书',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: 'no disclosure',
    },
    {
      key:
        '["LRs:0","A10:0"," If incorporated in the PRC, the kind of legal entity:0"]',
      schema: {
        data: {
          label: ' If incorporated in the PRC, the kind of legal entity',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: '',
        },
      },
      data: [],
      value: 'negative statement',
    },
    {
      key: '["LRs:0","A10:0","Particulars of the issued share capital:0"]',
      schema: {
        data: {
          label: 'Particulars of the issued share capital',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: '',
        },
      },
      data: [],
      value: 'disclosure',
    },
    {
      key: '["LRs:0","A10:1","Name of every subsidiary:0"]',
      schema: {
        data: {
          label: 'Name of every subsidiary',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: 'name of every sub discription',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 116.3641,
                box_top: 331.4614,
                box_right: 193.23489999999998,
                box_bottom: 353.32370000000003,
              },
              page: 3,
              text: '保荐人承诺',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: null,
    },
    {
      key: '["LRs:0","A10:1","Country of operation:0"]',
      schema: {
        data: {
          label: 'Country of operation',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: 'country of operation description',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 189.7087,
                box_top: 387.1751,
                box_right: 216.5077,
                box_bottom: 411.15319999999997,
              },
              page: 3,
              text: '损失',
            },
            {
              box: {
                box_left: 443.5941,
                box_top: 409.7427,
                box_right: 473.21410000000003,
                box_bottom: 436.5417,
              },
              page: 3,
              text: '出具',
            },
          ],
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 288.4419,
                box_top: 165.7307,
                box_right: 344.1556,
                box_bottom: 189.70880000000002,
              },
              page: 3,
              text: '法律责任',
            },
            {
              box: {
                box_left: 456.2883,
                box_top: 198.1716,
                box_right: 500.013,
                box_bottom: 221.4444,
              },
              page: 3,
              text: '控股股',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: 'no disclosure',
    },
    {
      key: '["LRs:0","A10:1","Country of incorporation:0"]',
      schema: {
        data: {
          label: 'Country of incorporation',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: '',
        },
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 424.5527,
                box_top: 327.23,
                box_right: 499.30780000000004,
                box_bottom: 355.4395,
              },
              page: 3,
              text: '出具的文件',
            },
          ],
          handleType: 'wireframe',
        },
        {
          boxes: [
            {
              box: {
                box_left: 358.9656,
                box_top: 441.4783,
                box_right: 472.5088,
                box_bottom: 464.04589999999996,
              },
              page: 3,
              text: '给他人造成损失的',
            },
          ],
          handleType: 'wireframe',
        },
      ],
      value: 'negative statement',
    },
    {
      key:
        '["LRs:0","A10:1"," If incorporated in the PRC, the kind of legal entity:0"]',
      schema: {
        data: {
          label: ' If incorporated in the PRC, the kind of legal entity',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: '',
        },
      },
      data: [],
      value: 'disclosure',
    },
    {
      key: '["LRs:0","A10:1","Particulars of the issued share capital:0"]',
      schema: {
        data: {
          label: 'Particulars of the issued share capital',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: '',
        },
      },
      data: [],
      value: 'negative statement',
    },
  ],
};
