export const answer = {
  items: [
    {
      key: '["LRs","A1"]',
      schema: {
        data: {
          label: 'A1',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 93,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1002,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [{ boxes: [], handleType: 'wireframe', value: 'disclosure' }],
    },
    {
      key: '["LRs","A4"]',
      schema: {
        data: {
          label: 'A4',
          type: 'D/NS/N',
          multiple: true,
          required: false,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 431.9139,
                box_top: 65.209,
                box_right: 474.87510000000003,
                box_bottom: 88.2239,
              },
              page: 0,
              text: '2017',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A10","Name of every subsidiary"]',
      schema: {
        data: {
          label: 'Name of every subsidiary',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 66.2293,
                box_top: 94.507,
                box_right: 183.0608,
                box_bottom: 131.71450000000002,
              },
              page: 0,
              text: 'ComStage 1',
            },
          ],
          value: 'disclosure',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A10","Country of operation"]',
      schema: {
        data: {
          label: 'Country of operation',
          required: false,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 436.816,
                box_top: 68.4618,
                box_right: 482.2091,
                box_bottom: 93.7629,
              },
              page: 0,
              text: '2017',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A2"]',
      schema: {
        data: {
          label: 'A2',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 94,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1003,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 61.145615247074126,
                box_top: 95.22023407022107,
                box_right: 189.65422382964888,
                box_bottom: 121.5412743823147,
              },
              page: 0,
              text: 'ComStage 1',
            },
          ],
          handleType: 'wireframe',
          value: 'no disclosure',
        },
        {
          boxes: [
            {
              box: {
                box_left: 404.09328754876464,
                box_top: 511.71198959687905,
                box_right: 456.7353681729519,
                box_bottom: 523.3242132639792,
              },
              page: 0,
              text: 'in conformity',
            },
          ],
          handleType: 'wireframe',
          value: 'no disclosure',
        },
      ],
    },
    {
      key: '["LRs","A3"]',
      schema: {
        data: {
          label: 'A3',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 95,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1004,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 29.4055372236671,
                box_top: 143.21742522756827,
                box_right: 137.786291449935,
                box_bottom: 168.76431729518856,
              },
              page: 1,
              text: 'Table of contents:',
            },
          ],
          handleType: 'wireframe',
          value: 'negative statement',
        },
      ],
    },
    {
      key: '["LRs","A5"]',
      schema: {
        data: {
          label: 'A5',
          type: 'D/NS/N',
          required: false,
          multi: true,
          words: '',
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _index: 97,
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1006,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 248.4894904096229,
                box_top: 109.15490247074122,
                box_right: 347.5804657022107,
                box_bottom: 154.8296488946684,
              },
              page: 2,
              text: 'DIRECTORY',
            },
          ],
          handleType: 'wireframe',
          value: 'disclosure',
        },
      ],
    },
  ],
};

export const schema = {
  schemas: [
    {
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
      ],
      schema: {
        A16: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A16',
          _index: 18,
        },
        A4: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A4',
          _index: 6,
        },
        A1: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A1',
          _index: 3,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 9,
        },
        A27: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A27',
          _index: 29,
        },
        A19: {
          required: false,
          type: 'A19',
          multi: true,
          name: 'A19',
          _index: 21,
        },
        A15: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A15',
          _index: 17,
        },
        A24: {
          required: false,
          type: 'A24',
          multi: true,
          name: 'A24',
          _index: 26,
        },
        A20: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A20',
          _index: 22,
        },
        A32: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A32',
          _index: 34,
        },
        A31: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A31',
          _index: 33,
        },
        A33: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A33',
          _index: 35,
        },
        A11: {
          required: false,
          type: 'A11',
          multi: true,
          name: 'A11',
          _index: 13,
        },
        A26: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A26',
          _index: 28,
        },
        A9: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A9',
          _index: 11,
        },
        A29: {
          required: false,
          type: 'A29',
          multi: true,
          name: 'A29',
          _index: 31,
        },
        A23: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A23',
          _index: 25,
        },
        A14: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A14',
          _index: 16,
        },
        A17: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A17',
          _index: 19,
        },
        A18: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A18',
          _index: 20,
        },
        A22: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A22',
          _index: 24,
        },
        A5: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A5',
          _index: 7,
        },
        A10: {
          required: false,
          type: 'A10',
          multi: true,
          name: 'A10',
          _index: 12,
        },
        A28: {
          required: false,
          type: 'A28',
          multi: true,
          name: 'A28',
          _index: 30,
        },
        A30: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A30',
          _index: 32,
        },
        A21: {
          required: false,
          type: 'A21',
          multi: true,
          name: 'A21',
          _index: 23,
        },
        A2: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A2',
          _index: 4,
        },
        A12: {
          required: false,
          type: 'A12',
          multi: true,
          name: 'A12',
          _index: 14,
        },
        A3: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A3',
          _index: 5,
        },
        A8: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A8',
          _index: 10,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 8,
        },
        A13: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A13',
          _index: 15,
        },
        A25: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A25',
          _index: 27,
        },
      },
      name: 'LRs',
    },
    {
      orders: ['A6', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 38,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 37,
        },
      },
      name: 'A6',
    },
    {
      orders: ['A7', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 41,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 40,
        },
      },
      name: 'A7',
    },
    {
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.2': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.2',
          _index: 44,
        },
        'A9.1': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.1',
          _index: 43,
        },
      },
      name: 'A9',
    },
    {
      orders: [
        'Name of every subsidiary',
        'Country of operation',
        'Country of incorporation',
        ' If incorporated in the PRC, the kind of legal entity',
        'Particulars of the issued share capital',
        'Debt securities of every subsidiary',
      ],
      schema: {
        'Debt securities of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Debt securities of every subsidiary',
          _index: 51,
        },
        'Name of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Name of every subsidiary',
          _index: 46,
        },
        'Country of operation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of operation',
          _index: 47,
        },
        'Particulars of the issued share capital': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 50,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 49,
        },
        'Country of incorporation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of incorporation',
          _index: 48,
        },
      },
      name: 'A10',
    },
    {
      orders: ['List of directors', 'Biography'],
      schema: {
        'List of directors': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'List of directors',
          _index: 53,
        },
        Biography: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Biography',
          _index: 54,
        },
      },
      name: 'A12',
    },
    {
      orders: ['A19.1', 'A19.2'],
      schema: {
        'A19.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.1',
          _index: 56,
        },
        'A19.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.2',
          _index: 57,
        },
      },
      name: 'A19',
    },
    {
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 59,
        },
        A23: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A23',
          _index: 60,
        },
      },
      name: 'A23',
    },
    {
      orders: ['Table for related party transaction', 'Details'],
      schema: {
        Details: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Details',
          _index: 63,
        },
        'Table for related party transaction': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Table for related party transaction',
          _index: 62,
        },
      },
      name: 'A24',
    },
    {
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 65,
        },
        A25: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A25',
          _index: 66,
        },
      },
      name: 'A25',
    },
    {
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 68,
        },
        A26: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A26',
          _index: 69,
        },
      },
      name: 'A26',
    },
    {
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 71,
        },
        A27: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A27',
          _index: 72,
        },
      },
      name: 'A27',
    },
    {
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        ' Shortfall of the profit guarantee',
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
      ],
      schema: {
        ' Shortfall of the profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 75,
        },
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 74,
        },
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 76,
        },
      },
      name: 'A28',
    },
    { orders: [], schema: {}, name: 'text' },
    {
      orders: ['A29.1', 'A29.2', 'A29.3'],
      schema: {
        'A29.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.3',
          _index: 81,
        },
        'A29.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.1',
          _index: 79,
        },
        'A29.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.2',
          _index: 80,
        },
      },
      name: 'A29',
    },
    {
      orders: ['A11.1', 'A11.2', 'A11.3'],
      schema: {
        'A11.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.3',
          _index: 85,
        },
        'A11.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.2',
          _index: 84,
        },
        'A11.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.1',
          _index: 83,
        },
      },
      name: 'A11',
    },
    {
      orders: ['A21.1', 'A21.2', 'A21.3', 'A21.4', 'A21.5'],
      schema: {
        'A21.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.3',
          _index: 89,
        },
        'A21.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.2',
          _index: 88,
        },
        'A21.4': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.4',
          _index: 90,
        },
        'A21.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.1',
          _index: 87,
        },
        'A21.5': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.5',
          _index: 91,
        },
      },
      name: 'A21',
    },
  ],
  schema_types: [
    {
      label: 'Y/N',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
      ],
      type: 'enum',
    },
    {
      label: 'Y/N/NA',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        { isDefault: false, name: 'N/A' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N/In',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
        { isDefault: false, name: 'Insufficient' },
      ],
      type: 'enum',
    },
    {
      label: 'P/F',
      values: [
        { isDefault: false, name: 'pass' },
        { isDefault: false, name: 'fail' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
      ],
      type: 'enum',
    },
  ],
  version: 'dadc4cbce001c2db744a3f5b61600c4a',
};

export const answered10 = [
  {
    key: '["LRs","A1"]',
    schema: {
      data: {
        label: 'A1',
        type: 'D/NS/N',
        required: false,
        multi: true,
        words: 'A1 description',
      },
      meta: {
        _path: ['LRs', 'D/NS/N'],
        _index: 636,
        _partType: 'top',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1002,
        _parent: ['LRs'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 328.97659143368,
              box_top: 205.90821326397915,
              box_right: 415.6747864921976,
              box_bottom: 267.06140442132636,
            },
            page: 2,
            text: '非執行董事\n蔣年（主席）\n肖焱\n鄔燕敏',
          },
        ],
        handleType: 'wireframe',
        value: 'disclosure',
      },
    ],
  },
  {
    key: '["LRs","A2"]',
    schema: {
      data: {
        label: 'A2',
        type: 'D/NS/N',
        required: false,
        multi: true,
        words: '',
      },
      meta: {
        _path: ['LRs', 'D/NS/N'],
        _index: 637,
        _partType: 'top',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1003,
        _parent: ['LRs'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 330.52477348829643,
              box_top: 585.2128166449934,
              box_right: 473.731613540312,
              box_bottom: 644.043734720416,
            },
            page: 2,
            text:
              '法律顧問\n盛德律師事務所\n香港中環\n國際金融中心二期三十九樓',
          },
        ],
        handleType: 'wireframe',
        value: 'negative statement',
      },
    ],
  },
  {
    key: '["LRs","A3"]',
    schema: {
      data: {
        label: 'A3',
        type: 'D/NS/N',
        required: false,
        multi: true,
        words: 'A3 description',
      },
      meta: {
        _path: ['LRs', 'D/NS/N'],
        _index: 638,
        _partType: 'top',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1004,
        _parent: ['LRs'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 358.39205047139137,
              box_top: 135.46592977893366,
              box_right: 500.05070846879056,
              box_bottom: 195.07093888166446,
            },
            page: 3,
            text:
              '註冊辦事處\nClarendon House\n2 Church Street\nHamilton HM 11, Bermuda',
          },
        ],
        handleType: 'wireframe',
        value: 'disclosure',
      },
    ],
  },
  {
    key: '["LRs","A4"]',
    schema: {
      data: {
        label: 'A4',
        type: 'D/NS/N',
        required: false,
        multi: true,
        words: '',
      },
      meta: {
        _path: ['LRs', 'D/NS/N'],
        _index: 639,
        _partType: 'top',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1005,
        _parent: ['LRs'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 337.49159273407014,
              box_top: 160.23684265279581,
              box_right: 534.8848046976592,
              box_bottom: 260.0945851755526,
            },
            page: 4,
            text:
              '本人謹代表領航醫藥及生物科技有限公司\n（「本公司」，連同其附屬公司統稱「本集\n團」）之董事（「董事」）會（「董事會」）向本\n公司所有股東（「股東」）提呈本集團截至\n二零一八年三月三十一日止年度（「本財政\n年度」）之經審核綜合財務業績與截至二零\n一七年三月三十一日止年度（「上一財政年\n度」）之比較數字。',
          },
        ],
        handleType: 'wireframe',
        value: 'no disclosure',
      },
    ],
  },
  {
    key: '["LRs","A5"]',
    schema: {
      data: {
        label: 'A5',
        type: 'D/NS/N',
        required: false,
        multi: true,
        words: '',
      },
      meta: {
        _path: ['LRs', 'D/NS/N'],
        _index: 640,
        _partType: 'top',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1006,
        _parent: ['LRs'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 331.29886451560463,
              box_top: 384.72324057217156,
              box_right: 526.369803397269,
              box_bottom: 538.7673550065018,
            },
            page: 4,
            text:
              '於本財政年度，本公司擁有人應佔虧損為\n約231,048,000港元，較上一財政年度錄得\n之本公司擁有人應佔虧損約169,788,000港\n元增加約28.3%。本財政年度虧損相對增\n加61,260,000港元主要由於精優藥業控股\n有限公司（「精優」）發行之可換股債券投資\n的衍生部分之公平值減少所致。精優是一\n間股份於香港聯合交易所有限公司（「聯交\n所」）主板上市之公司，主要於中華人民共\n和國（「中國」）從事向客戶推廣及分銷醫藥\n產品、於中國開發、製造及銷售醫藥產品\n以及基因相關技術的商業開發及研發之業',
          },
        ],
        handleType: 'wireframe',
        value: '',
      },
    ],
  },
  {
    key: '["LRs","A10","Name of every subsidiary"]',
    schema: {
      data: {
        label: 'Name of every subsidiary',
        required: false,
        multi: true,
        type: 'D/NS/N',
        words: '',
      },
      meta: {
        _index: 679,
        _path: ['LRs', 'A10', 'D/NS/N'],
        _partType: 'normal.schema',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1012,
        _parent: ['LRs', 'A10'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 358.39205047139137,
              box_top: 523.285534460338,
              box_right: 518.6288931241871,
              box_bottom: 597.5982730819245,
            },
            page: 5,
            text: '蔣年\n主席\n上海，二零一八年六月二十九日',
          },
        ],
        handleType: 'wireframe',
        value: 'disclosure',
      },
    ],
  },
  {
    key: '["LRs","A12","List of directors"]',
    schema: {
      data: {
        label: 'List of directors',
        required: false,
        multi: true,
        type: 'D/NS/N',
        words: '',
      },
      meta: {
        _index: 686,
        _path: ['LRs', 'A12', 'D/NS/N'],
        _partType: 'normal.schema',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1023,
        _parent: ['LRs', 'A12'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 60.36700495773731,
              box_top: 527.9300806241872,
              box_right: 205.89611809167747,
              box_bottom: 592.9537269180753,
            },
            page: 5,
            text: 'Jiang Nian\nChairman\nShanghai, 29 June 2018',
          },
        ],
        handleType: 'wireframe',
        value: 'negative statement',
      },
    ],
  },
  {
    key: '["LRs","A18"]',
    schema: {
      data: {
        label: 'A18',
        type: 'D/NS/N',
        required: false,
        multi: true,
        words: '',
      },
      meta: {
        _path: ['LRs', 'D/NS/N'],
        _index: 653,
        _partType: 'top',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1030,
        _parent: ['LRs'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 330.52477348829643,
              box_top: 611.5319115734719,
              box_right: 524.8216213426527,
              box_bottom: 688.166923276983,
            },
            page: 6,
            text:
              '研發\n現正進行之研發項目（「研發過程」）指涉及\n口服胰島素產品正在進行的研發項目。本\n集團將向研發過程的臨床試驗注入額外資\n源並綜合項目團隊的努力以促進其發展。',
          },
        ],
        handleType: 'wireframe',
        value: 'negative statement',
      },
    ],
  },
  {
    key: '["LRs","A19","A19.1"]',
    schema: {
      data: {
        label: 'A19.1',
        required: false,
        multi: true,
        type: 'D/NS/N',
        words: '',
      },
      meta: {
        _index: 689,
        _path: ['LRs', 'A19', 'D/NS/N'],
        _partType: 'normal.schema',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1032,
        _parent: ['LRs', 'A19'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 335.9434106794538,
              box_top: 421.1055188556566,
              box_right: 541.0775329161247,
              box_bottom: 534.8968998699609,
            },
            page: 8,
            text:
              '最初，本公司有意於中國平湖建立自己的\n生產工廠、研發實驗室及辦公室，以開發\n生物產業產品。然而，由於江蘇瑞峰建設\n集團有限公司作為原告與本公司之間接非\n全資附屬公司中荷（平湖）生物技術有限公\n司（「中荷（平湖）」）作為被告之間有關中國\n平湖相關建設服務的糾紛，使建設生產廠\n房、研發實驗室及辦公室，此後被擱置。',
          },
        ],
        handleType: 'wireframe',
        value: 'negative statement',
      },
    ],
  },
  {
    key: '["LRs","A26"]',
    schema: {
      data: {
        label: 'A26',
        type: 'D/NS/N',
        required: false,
        multi: true,
        words: '',
      },
      meta: {
        _path: ['LRs', 'D/NS/N'],
        _index: 661,
        _partType: 'top',
        _type: {
          label: 'D/NS/N',
          values: [
            { isDefault: false, name: 'disclosure' },
            { isDefault: false, name: 'negative statement' },
            { isDefault: false, name: 'no disclosure' },
          ],
          type: 'enum',
        },
        _isHide: false,
        _nodeIndex: 1047,
        _parent: ['LRs'],
      },
      children: [],
    },
    data: [
      {
        boxes: [
          {
            box: {
              box_left: 59.59291393042912,
              box_top: 388.5936957087125,
              box_right: 352.1993222529258,
              box_bottom: 552.7009934980493,
            },
            page: 9,
            text:
              'Research and development\nThe research and development segment represents an in-process\nresearch and development projects involving an oral insulin\nproduct (the “Product”) that the Group is developing. The Group\nis preparing to commence Part B of phase III clinical trials for the\nProduct (the “Clinical Trial”). In order to better prepare for the\nClinical Trial, the timetable for obtaining the Certificate of New\nMedicine and the Pharmaceutical Manufacturing Permit, subjec\nto the approval of State Drug Administration, and the generating\nrevenue of the Product has been slightly adjusted from the third\nquarter of 2019 and around end of 2019 to early of 2020 and\naround mid of 2020 respectively.',
          },
        ],
        handleType: 'wireframe',
        value: 'negative statement',
      },
    ],
  },
];
