/**
 * v2 -> v1时，二级节点信息不保存
 */
export const answer = {
  items: [
    {
      key: '["LRs","A1"]',
      schema: {
        data: {
          label: 'A1',
          type: 'D/NS/N',
          words: 'A1 description',
          multiple: true,
          required: false,
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 188.6211,
                box_top: 191.6881,
                box_right: 495.32210000000003,
                box_bottom: 262.22929999999997,
              },
              page: 0,
              text:
                'SAMSUNG S&P GSCI CRUDE OIL ER FUTURES ETF \n(Stock Code: 3175) \n(SUB-FUND OF SAMSUNG ETFS TRUST)',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A2"]',
      schema: {
        data: {
          label: 'A2',
          type: 'D/NS/N',
          words: '',
          multiple: true,
          required: false,
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 197.0554,
                box_top: 260.6959,
                box_right: 382.6095,
                box_bottom: 293.6663,
              },
              page: 0,
              text:
                'Reports and Financial Statements \nFor the year ended 31 March 2018',
            },
          ],
          value: 'disclosure',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A3"]',
      schema: {
        data: {
          label: 'A3',
          type: 'D/NS/N',
          words: 'A3 description',
          multiple: true,
          required: false,
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 197.165625,
                box_top: 277.20000000000005,
                box_right: 371.16562500000003,
                box_bottom: 295.8,
              },
              page: 0,
              text: 'For the year ended 31 March 2018',
            },
          ],
          value: 'disclosure',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A4"]',
      schema: {
        data: {
          label: 'A4',
          type: 'D/NS/N',
          words: '',
          multiple: true,
          required: false,
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 199.565625,
                box_top: 256.20000000000005,
                box_right: 373.565625,
                box_bottom: 297,
              },
              page: 0,
              text:
                'Reports and Financial Statements \nFor the year ended 31 March 2018',
            },
          ],
          value: 'disclosure',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A5"]',
      schema: {
        data: {
          label: 'A5',
          type: 'D/NS/N',
          words: '',
          multiple: true,
          required: false,
        },
        meta: {
          _path: ['LRs', 'D/NS/N'],
          _partType: 'top',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _parent: ['LRs'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 268.8,
                box_top: 219.6,
                box_right: 306,
                box_bottom: 234,
              },
              page: 0,
              text: '3175)',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A10","Name of every subsidiary"]',
      schema: {
        data: {
          label: 'Name of every subsidiary',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _index: 136,
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1012,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 339.27208533653845,
                box_top: 277.0673076923077,
                box_right: 377.68554687499994,
                box_bottom: 289.32692307692304,
              },
              page: 0,
              text: '2018',
            },
          ],
          value: 'disclosure',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A10","Country of operation"]',
      schema: {
        data: {
          label: 'Country of operation',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: 'country of operation description',
        },
        meta: {
          _index: 228,
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1013,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 459.41631610576917,
                box_top: 199.4230769230769,
                box_right: 488.0220853365384,
                box_bottom: 221.49038461538458,
              },
              page: 0,
              text: 'ETF',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
      ],
    },
    {
      key: '["LRs","A10","Country of incorporation"]',
      schema: {
        data: {
          label: 'Country of incorporation',
          required: false,
          multi: true,
          type: 'D/NS/N',
          words: '',
        },
        meta: {
          _index: 138,
          _path: ['LRs', 'A10', 'D/NS/N'],
          _partType: 'normal.schema',
          _type: {
            label: 'D/NS/N',
            values: [
              { isDefault: false, name: 'disclosure' },
              { isDefault: false, name: 'negative statement' },
              { isDefault: false, name: 'no disclosure' },
            ],
            type: 'enum',
          },
          _isHide: false,
          _nodeIndex: 1014,
          _parent: ['LRs', 'A10'],
        },
        children: [],
      },
      data: [
        {
          boxes: [
            {
              box: {
                box_left: 403.02208533653845,
                box_top: 196.15384615384613,
                box_right: 488.83939302884613,
                box_bottom: 223.94230769230768,
              },
              page: 0,
              text: 'FUTURES ETF',
            },
          ],
          value: 'negative statement',
          handleType: 'wireframe',
        },
      ],
    },
  ],
};

export const schema = {
  schemas: [
    {
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
      ],
      schema: {
        A16: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A16',
          _index: 18,
        },
        A4: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A4',
          _index: 6,
        },
        A1: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A1',
          _index: 3,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 9,
        },
        A27: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A27',
          _index: 29,
        },
        A19: {
          required: false,
          type: 'A19',
          multi: true,
          name: 'A19',
          _index: 21,
        },
        A15: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A15',
          _index: 17,
        },
        A24: {
          required: false,
          type: 'A24',
          multi: true,
          name: 'A24',
          _index: 26,
        },
        A20: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A20',
          _index: 22,
        },
        A32: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A32',
          _index: 34,
        },
        A31: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A31',
          _index: 33,
        },
        A33: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A33',
          _index: 35,
        },
        A11: {
          required: false,
          type: 'A11',
          multi: true,
          name: 'A11',
          _index: 13,
        },
        A26: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A26',
          _index: 28,
        },
        A9: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A9',
          _index: 11,
        },
        A29: {
          required: false,
          type: 'A29',
          multi: true,
          name: 'A29',
          _index: 31,
        },
        A23: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A23',
          _index: 25,
        },
        A14: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A14',
          _index: 16,
        },
        A17: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A17',
          _index: 19,
        },
        A18: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A18',
          _index: 20,
        },
        A22: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A22',
          _index: 24,
        },
        A5: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A5',
          _index: 7,
        },
        A10: {
          required: false,
          type: 'A10',
          multi: true,
          name: 'A10',
          _index: 12,
        },
        A28: {
          required: false,
          type: 'A28',
          multi: true,
          name: 'A28',
          _index: 30,
        },
        A30: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A30',
          _index: 32,
        },
        A21: {
          required: false,
          type: 'A21',
          multi: true,
          name: 'A21',
          _index: 23,
        },
        A2: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A2',
          _index: 4,
        },
        A12: {
          required: false,
          type: 'A12',
          multi: true,
          name: 'A12',
          _index: 14,
        },
        A3: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A3',
          _index: 5,
        },
        A8: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A8',
          _index: 10,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 8,
        },
        A13: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A13',
          _index: 15,
        },
        A25: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A25',
          _index: 27,
        },
      },
      name: 'LRs',
    },
    {
      orders: ['A6', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 38,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 37,
        },
      },
      name: 'A6',
    },
    {
      orders: ['A7', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 41,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 40,
        },
      },
      name: 'A7',
    },
    {
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.2': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.2',
          _index: 44,
        },
        'A9.1': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.1',
          _index: 43,
        },
      },
      name: 'A9',
    },
    {
      orders: [
        'Name of every subsidiary',
        'Country of operation',
        'Country of incorporation',
        ' If incorporated in the PRC, the kind of legal entity',
        'Particulars of the issued share capital',
        'Debt securities of every subsidiary',
      ],
      schema: {
        'Debt securities of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Debt securities of every subsidiary',
          _index: 51,
        },
        'Name of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Name of every subsidiary',
          _index: 46,
        },
        'Country of operation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of operation',
          _index: 47,
        },
        'Particulars of the issued share capital': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 50,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 49,
        },
        'Country of incorporation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of incorporation',
          _index: 48,
        },
      },
      name: 'A10',
    },
    {
      orders: ['List of directors', 'Biography'],
      schema: {
        'List of directors': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'List of directors',
          _index: 53,
        },
        Biography: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Biography',
          _index: 54,
        },
      },
      name: 'A12',
    },
    {
      orders: ['A19.1', 'A19.2'],
      schema: {
        'A19.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.1',
          _index: 56,
        },
        'A19.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.2',
          _index: 57,
        },
      },
      name: 'A19',
    },
    {
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 59,
        },
        A23: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A23',
          _index: 60,
        },
      },
      name: 'A23',
    },
    {
      orders: ['Table for related party transaction', 'Details'],
      schema: {
        Details: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Details',
          _index: 63,
        },
        'Table for related party transaction': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Table for related party transaction',
          _index: 62,
        },
      },
      name: 'A24',
    },
    {
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 65,
        },
        A25: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A25',
          _index: 66,
        },
      },
      name: 'A25',
    },
    {
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 68,
        },
        A26: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A26',
          _index: 69,
        },
      },
      name: 'A26',
    },
    {
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 71,
        },
        A27: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A27',
          _index: 72,
        },
      },
      name: 'A27',
    },
    {
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        ' Shortfall of the profit guarantee',
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
      ],
      schema: {
        ' Shortfall of the profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 75,
        },
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 74,
        },
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 76,
        },
      },
      name: 'A28',
    },
    { orders: [], schema: {}, name: 'text' },
    {
      orders: ['A29.1', 'A29.2', 'A29.3'],
      schema: {
        'A29.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.3',
          _index: 81,
        },
        'A29.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.1',
          _index: 79,
        },
        'A29.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.2',
          _index: 80,
        },
      },
      name: 'A29',
    },
    {
      orders: ['A11.1', 'A11.2', 'A11.3'],
      schema: {
        'A11.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.3',
          _index: 85,
        },
        'A11.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.2',
          _index: 84,
        },
        'A11.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.1',
          _index: 83,
        },
      },
      name: 'A11',
    },
    {
      orders: ['A21.1', 'A21.2', 'A21.3', 'A21.4', 'A21.5'],
      schema: {
        'A21.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.3',
          _index: 89,
        },
        'A21.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.2',
          _index: 88,
        },
        'A21.4': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.4',
          _index: 90,
        },
        'A21.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.1',
          _index: 87,
        },
        'A21.5': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.5',
          _index: 91,
        },
      },
      name: 'A21',
    },
  ],
  schema_types: [
    {
      label: 'Y/N',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
      ],
      type: 'enum',
    },
    {
      label: 'Y/N/NA',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        { isDefault: false, name: 'N/A' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N/In',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
        { isDefault: false, name: 'Insufficient' },
      ],
      type: 'enum',
    },
    {
      label: 'P/F',
      values: [
        { isDefault: false, name: 'pass' },
        { isDefault: false, name: 'fail' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
      ],
      type: 'enum',
    },
  ],
  version: 'dadc4cbce001c2db744a3f5b61600c4a',
};
