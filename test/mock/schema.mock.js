export const schema = {
  schemas: [
    {
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
      ],
      schema: {
        A16: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A16',
          _index: 18,
        },
        A4: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A4',
          _index: 6,
        },
        A1: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A1',
          _index: 3,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 9,
        },
        A27: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A27',
          _index: 29,
        },
        A19: {
          required: false,
          type: 'A19',
          multi: true,
          name: 'A19',
          _index: 21,
        },
        A15: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A15',
          _index: 17,
        },
        A24: {
          required: false,
          type: 'A24',
          multi: true,
          name: 'A24',
          _index: 26,
        },
        A20: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A20',
          _index: 22,
        },
        A32: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A32',
          _index: 34,
        },
        A31: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A31',
          _index: 33,
        },
        A33: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A33',
          _index: 35,
        },
        A11: {
          required: false,
          type: 'A11',
          multi: true,
          name: 'A11',
          _index: 13,
        },
        A26: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A26',
          _index: 28,
        },
        A9: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A9',
          _index: 11,
        },
        A29: {
          required: false,
          type: 'A29',
          multi: true,
          name: 'A29',
          _index: 31,
        },
        A23: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A23',
          _index: 25,
        },
        A14: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A14',
          _index: 16,
        },
        A17: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A17',
          _index: 19,
        },
        A18: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A18',
          _index: 20,
        },
        A22: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A22',
          _index: 24,
        },
        A5: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A5',
          _index: 7,
        },
        A10: {
          required: false,
          type: 'A10',
          multi: true,
          name: 'A10',
          _index: 12,
        },
        A28: {
          required: false,
          type: 'A28',
          multi: true,
          name: 'A28',
          _index: 30,
        },
        A30: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A30',
          _index: 32,
        },
        A21: {
          required: false,
          type: 'A21',
          multi: true,
          name: 'A21',
          _index: 23,
        },
        A2: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A2',
          _index: 4,
        },
        A12: {
          required: false,
          type: 'A12',
          multi: true,
          name: 'A12',
          _index: 14,
        },
        A3: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A3',
          _index: 5,
        },
        A8: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A8',
          _index: 10,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 8,
        },
        A13: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A13',
          _index: 15,
        },
        A25: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A25',
          _index: 27,
        },
      },
      name: 'LRs',
    },
    {
      orders: ['A6', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 38,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 37,
        },
      },
      name: 'A6',
    },
    {
      orders: ['A7', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 41,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 40,
        },
      },
      name: 'A7',
    },
    {
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.2': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.2',
          _index: 44,
        },
        'A9.1': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.1',
          _index: 43,
        },
      },
      name: 'A9',
    },
    {
      orders: [
        'Name of every subsidiary',
        'Country of operation',
        'Country of incorporation',
        ' If incorporated in the PRC, the kind of legal entity',
        'Particulars of the issued share capital',
        'Debt securities of every subsidiary',
      ],
      schema: {
        'Debt securities of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Debt securities of every subsidiary',
          _index: 51,
        },
        'Name of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Name of every subsidiary',
          _index: 46,
        },
        'Country of operation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of operation',
          _index: 47,
        },
        'Particulars of the issued share capital': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 50,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 49,
        },
        'Country of incorporation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of incorporation',
          _index: 48,
        },
      },
      name: 'A10',
    },
    {
      orders: ['List of directors', 'Biography'],
      schema: {
        'List of directors': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'List of directors',
          _index: 53,
        },
        Biography: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Biography',
          _index: 54,
        },
      },
      name: 'A12',
    },
    {
      orders: ['A19.1', 'A19.2'],
      schema: {
        'A19.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.1',
          _index: 56,
        },
        'A19.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.2',
          _index: 57,
        },
      },
      name: 'A19',
    },
    {
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 59,
        },
        A23: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A23',
          _index: 60,
        },
      },
      name: 'A23',
    },
    {
      orders: ['Table for related party transaction', 'Details'],
      schema: {
        Details: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Details',
          _index: 63,
        },
        'Table for related party transaction': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Table for related party transaction',
          _index: 62,
        },
      },
      name: 'A24',
    },
    {
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 65,
        },
        A25: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A25',
          _index: 66,
        },
      },
      name: 'A25',
    },
    {
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 68,
        },
        A26: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A26',
          _index: 69,
        },
      },
      name: 'A26',
    },
    {
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 71,
        },
        A27: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A27',
          _index: 72,
        },
      },
      name: 'A27',
    },
    {
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        ' Shortfall of the profit guarantee',
        ' disclosure of whether the counterparty compensates the shortfall of profit guarantee',
      ],
      schema: {
        ' Shortfall of the profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 75,
        },
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 74,
        },
        ' disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            ' disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 76,
        },
      },
      name: 'A28',
    },
    { orders: [], schema: {}, name: 'text' },
    {
      orders: ['A29.1', 'A29.2', 'A29.3'],
      schema: {
        'A29.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.3',
          _index: 81,
        },
        'A29.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.1',
          _index: 79,
        },
        'A29.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.2',
          _index: 80,
        },
      },
      name: 'A29',
    },
    {
      orders: ['A11.1', 'A11.2', 'A11.3'],
      schema: {
        'A11.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.3',
          _index: 85,
        },
        'A11.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.2',
          _index: 84,
        },
        'A11.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.1',
          _index: 83,
        },
      },
      name: 'A11',
    },
    {
      orders: ['A21.1', 'A21.2', 'A21.3', 'A21.4', 'A21.5'],
      schema: {
        'A21.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.3',
          _index: 89,
        },
        'A21.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.2',
          _index: 88,
        },
        'A21.4': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.4',
          _index: 90,
        },
        'A21.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.1',
          _index: 87,
        },
        'A21.5': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.5',
          _index: 91,
        },
      },
      name: 'A21',
    },
  ],
};

export const schema2 = {
  schema_types: [
    {
      values: [
        { isDefault: false, name: '是' },
        { isDefault: false, name: '否' },
      ],
      type: 'enum',
      label: '是/否',
    },
    {
      values: [
        { isDefault: false, name: 'Y' },
        { isDefault: false, name: 'N' },
        { isDefault: false, name: 'NA' },
      ],
      type: 'enum',
      label: 'Y/N/NA',
    },
  ],
  schemas: [
    {
      orders: ['A1', 'A2', 'A3', 'A4', 'A5', 'A6', 'A7'],
      name: 'listing rules',
      schema: {
        A2: {
          required: false,
          multi: true,
          type: '是/否',
          name: 'A2',
          _index: 4,
        },
        A5: {
          required: false,
          multi: true,
          type: '文本',
          name: 'A5',
          _index: 7,
        },
        A4: {
          required: false,
          multi: true,
          type: '文本',
          name: 'A4',
          _index: 6,
        },
        A3: {
          required: false,
          multi: true,
          type: '文本',
          name: 'A3',
          _index: 5,
        },
        A6: {
          required: false,
          multi: true,
          type: '文本',
          name: 'A6',
          _index: 8,
        },
        A7: { required: false, multi: true, type: 'A7', name: 'A7', _index: 9 },
        A1: {
          required: false,
          multi: true,
          type: '是/否',
          name: 'A1',
          _index: 3,
        },
      },
    },
    { orders: [], name: 'text', schema: {} },
    {
      orders: ['A7.1', 'A7.2', 'A7.3'],
      name: 'A7',
      schema: {
        'A7.3': {
          required: false,
          multi: true,
          type: '文本',
          name: 'A7.3',
          _index: 14,
        },
        'A7.1': {
          required: false,
          multi: true,
          type: '文本',
          name: 'A7.1',
          _index: 12,
        },
        'A7.2': {
          required: false,
          multi: true,
          type: '文本',
          name: 'A7.2',
          _index: 13,
        },
      },
    },
  ],
  version: '027c87d53d2163984ee18df6fe956d85',
};

export const schemaA33 = {
  schemas: [
    {
      orders: [
        'A1',
        'A2',
        'A3',
        'A4',
        'A5',
        'A6',
        'A7',
        'A8',
        'A9',
        'A10',
        'A11',
        'A12',
        'A13',
        'A14',
        'A15',
        'A16',
        'A17',
        'A18',
        'A19',
        'A20',
        'A21',
        'A22',
        'A23',
        'A24',
        'A25',
        'A26',
        'A27',
        'A28',
        'A29',
        'A30',
        'A31',
        'A32',
        'A33',
      ],
      schema: {
        A16: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A16',
          _index: 109,
        },
        A4: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A4',
          _index: 97,
        },
        A1: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A1',
          _index: 94,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 100,
        },
        A27: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A27',
          _index: 120,
        },
        A19: {
          required: false,
          type: 'A19',
          multi: true,
          name: 'A19',
          _index: 112,
        },
        A15: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A15',
          _index: 108,
        },
        A24: {
          required: false,
          type: 'A24',
          multi: true,
          name: 'A24',
          _index: 117,
        },
        A20: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A20',
          _index: 113,
        },
        A32: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A32',
          _index: 125,
        },
        A31: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A31',
          _index: 124,
        },
        A33: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A33',
          _index: 126,
        },
        A11: {
          required: false,
          type: 'A11',
          multi: true,
          name: 'A11',
          _index: 104,
        },
        A26: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A26',
          _index: 119,
        },
        A9: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A9',
          _index: 102,
        },
        A29: {
          required: false,
          type: 'A29',
          multi: true,
          name: 'A29',
          _index: 122,
        },
        A23: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A23',
          _index: 116,
        },
        A14: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A14',
          _index: 107,
        },
        A17: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A17',
          _index: 110,
        },
        A18: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A18',
          _index: 111,
        },
        A22: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A22',
          _index: 115,
        },
        A5: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A5',
          _index: 98,
        },
        A10: {
          required: false,
          type: 'A10',
          multi: true,
          name: 'A10',
          _index: 103,
        },
        A28: {
          required: false,
          type: 'A28',
          multi: true,
          name: 'A28',
          _index: 121,
        },
        A30: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A30',
          _index: 123,
        },
        A21: {
          required: false,
          type: 'A21',
          multi: true,
          name: 'A21',
          _index: 114,
        },
        A2: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A2',
          _index: 95,
        },
        A12: {
          required: false,
          type: 'A12',
          multi: true,
          name: 'A12',
          _index: 105,
        },
        A3: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A3',
          _index: 96,
        },
        A8: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A8',
          _index: 101,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 99,
        },
        A13: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A13',
          _index: 106,
        },
        A25: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A25',
          _index: 118,
        },
      },
      name: 'LRs',
    },
    {
      orders: ['A6', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 129,
        },
        A6: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A6',
          _index: 128,
        },
      },
      name: 'A6',
    },
    {
      orders: ['A7', 'CONDITION'],
      schema: {
        CONDITION: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'CONDITION',
          _index: 132,
        },
        A7: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A7',
          _index: 131,
        },
      },
      name: 'A7',
    },
    {
      orders: ['A9.1', 'A9.2'],
      schema: {
        'A9.2': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.2',
          _index: 135,
        },
        'A9.1': {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'A9.1',
          _index: 134,
        },
      },
      name: 'A9',
    },
    {
      orders: [
        'Name of every subsidiary',
        'Country of operation',
        'Country of incorporation',
        ' If incorporated in the PRC, the kind of legal entity',
        'Particulars of the issued share capital',
        'Debt securities of every subsidiary',
      ],
      schema: {
        'Debt securities of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Debt securities of every subsidiary',
          _index: 142,
        },
        'Name of every subsidiary': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Name of every subsidiary',
          _index: 137,
        },
        'Country of operation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of operation',
          _index: 138,
        },
        'Particulars of the issued share capital': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Particulars of the issued share capital',
          _index: 141,
        },
        ' If incorporated in the PRC, the kind of legal entity': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' If incorporated in the PRC, the kind of legal entity',
          _index: 140,
        },
        'Country of incorporation': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Country of incorporation',
          _index: 139,
        },
      },
      name: 'A10',
    },
    {
      orders: ['List of directors', 'Biography'],
      schema: {
        'List of directors': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'List of directors',
          _index: 144,
        },
        Biography: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Biography',
          _index: 145,
        },
      },
      name: 'A12',
    },
    {
      orders: ['A19.1', 'A19.2'],
      schema: {
        'A19.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.1',
          _index: 147,
        },
        'A19.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A19.2',
          _index: 148,
        },
      },
      name: 'A19',
    },
    {
      orders: ['CONDITION', 'A23'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 150,
        },
        A23: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A23',
          _index: 151,
        },
      },
      name: 'A23',
    },
    {
      orders: ['Table for related party transaction', 'Details'],
      schema: {
        Details: {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Details',
          _index: 154,
        },
        'Table for related party transaction': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'Table for related party transaction',
          _index: 153,
        },
      },
      name: 'A24',
    },
    {
      orders: ['CONDITION', 'A25'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 156,
        },
        A25: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A25',
          _index: 157,
        },
      },
      name: 'A25',
    },
    {
      orders: ['CONDITION', 'A26'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 159,
        },
        A26: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A26',
          _index: 160,
        },
      },
      name: 'A26',
    },
    {
      orders: ['CONDITION', 'A27'],
      schema: {
        CONDITION: {
          required: false,
          type: 'Y/N',
          multi: true,
          name: 'CONDITION',
          _index: 162,
        },
        A27: {
          required: false,
          type: 'Y/N/NA',
          multi: true,
          name: 'A27',
          _index: 163,
        },
      },
      name: 'A27',
    },
    {
      orders: [
        'whether the annual reports disclosed the fulfilment of profit guarantee',
        ' Shortfall of the profit guarantee',
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
      ],
      schema: {
        ' Shortfall of the profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: ' Shortfall of the profit guarantee',
          _index: 166,
        },
        'whether the annual reports disclosed the fulfilment of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            'whether the annual reports disclosed the fulfilment of profit guarantee',
          _index: 165,
        },
        '  disclosure of whether the counterparty compensates the shortfall of profit guarantee': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name:
            '  disclosure of whether the counterparty compensates the shortfall of profit guarantee',
          _index: 167,
        },
      },
      name: 'A28',
    },
    { orders: [], schema: {}, name: 'text' },
    {
      orders: ['A29.1', 'A29.2', 'A29.3'],
      schema: {
        'A29.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.3',
          _index: 172,
        },
        'A29.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.1',
          _index: 170,
        },
        'A29.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A29.2',
          _index: 171,
        },
      },
      name: 'A29',
    },
    {
      orders: ['A11.1', 'A11.2', 'A11.3'],
      schema: {
        'A11.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.3',
          _index: 176,
        },
        'A11.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.2',
          _index: 175,
        },
        'A11.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A11.1',
          _index: 174,
        },
      },
      name: 'A11',
    },
    {
      orders: ['A21.1', 'A21.2', 'A21.3', 'A21.4', 'A21.5'],
      schema: {
        'A21.3': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.3',
          _index: 180,
        },
        'A21.2': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.2',
          _index: 179,
        },
        'A21.4': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.4',
          _index: 181,
        },
        'A21.1': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.1',
          _index: 178,
        },
        'A21.5': {
          required: false,
          type: 'D/NS/N',
          multi: true,
          name: 'A21.5',
          _index: 182,
        },
      },
      name: 'A21',
    },
  ],
  schema_types: [
    {
      label: 'Y/N',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
      ],
      type: 'enum',
    },
    {
      label: 'Y/N/NA',
      values: [
        { isDefault: false, name: 'Yes' },
        { isDefault: false, name: 'No' },
        { isDefault: false, name: 'N/A' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N/In',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
        { isDefault: false, name: 'Insufficient' },
      ],
      type: 'enum',
    },
    {
      label: 'P/F',
      values: [
        { isDefault: false, name: 'pass' },
        { isDefault: false, name: 'fail' },
      ],
      type: 'enum',
    },
    {
      label: 'D/NS/N',
      values: [
        { isDefault: false, name: 'disclosure' },
        { isDefault: false, name: 'negative statement' },
        { isDefault: false, name: 'no disclosure' },
      ],
      type: 'enum',
    },
  ],
  version: 'dadc4cbce001c2db744a3f5b61600c4a',
};
