'use strict';

const path = require('path');
const fs = require('fs');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlMinifier = require('html-minifier');
const { GitRevisionPlugin } = require('git-revision-webpack-plugin');
const {
  GenerateVersionFileWebpackPlugin,
} = require('@paoding/fe-version-plugin');
const {
  CopyUnsupportedBrowserWebpackPlugin,
} = require('unsupported-browser-plugin');
const { loadEnvVariable } = require('@paoding/vue-cli-env-adaptor');
const {
  getRouterPath,
  getThemePath,
  getVariablesPath,
  getPdfViewer,
} = require('./custom.config');

loadEnvVariable(__dirname);

const gitRevisionPlugin = new GitRevisionPlugin();

const pdfViewer = process.env.VUE_APP_PDF_VIEWER;
const favicon = process.env.VUE_APP_FAVICON;
const dist = process.env.VUE_APP_DIST;

const copyPluginList = [];

const webpackPlugins = [new CopyUnsupportedBrowserWebpackPlugin()];

if (pdfViewer === 'PDF.js') {
  copyPluginList.push({
    from: path.resolve(
      __dirname,
      './node_modules/@document-kits/viewer/dist/generic',
    ),
    to: path.posix.join('static', 'next-pdf'),
    toType: 'dir',
    ignore: ['.*'],
  });
} else if (pdfViewer === 'document-viewer') {
  copyPluginList.push({
    from: path.resolve(__dirname, './node_modules/pdf-document-viewer/dist'),
    to: path.posix.join('static', 'pdf-document-viewer'),
    transform(content, path) {
      if (dist === 'GFFUND') {
        if (path.endsWith('pdf.worker.js')) {
          const polyfillFileContent = fs.readFileSync(
            './node_modules/@paoding/pdf-document-viewer-polyfill/dist/index.js',
            'utf-8',
          );
          return `${polyfillFileContent}\n${content}`;
        }
      }

      return content;
    },
    toType: 'dir',
    ignore: ['.*'],
  });

  if (dist === 'HKEX') {
    copyPluginList.push(
      {
        from: path.resolve(__dirname, './node_modules/pdfjs/web'),
        to: path.posix.join('static', 'pdf-document-viewer/web'),
        toType: 'dir',
        ignore: ['.*'],
      },
      {
        from: path.resolve(
          __dirname,
          './src/custom/hkex/static',
        ),
        to: '',
        transform: (content) => {
          return HtmlMinifier.minify(content.toString(), {
            minifyCSS: true,
            minifyJS: true,
            collapseWhitespace: true,
            removeComments: true,
          });
        },
      },
    );

    webpackPlugins.push(
      new GenerateVersionFileWebpackPlugin(gitRevisionPlugin.commithash()),
    );
  }
}

if (favicon) {
  copyPluginList.push({
    from: path.resolve(__dirname, './static/', favicon),
    to: '',
  });
}

module.exports = {
  publicPath: './',
  assetsDir: 'static',
  productionSourceMap: false,
  devServer: {
    proxy: {
      '/api': {
        changeOrigin: true,
        target: process.env.VUE_APP_PROXY_TARGET,
      },
    },
    compress: false,
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: 'expanded' },
        additionalData: `@import "${getVariablesPath(dist)}";`,
      },
    },
  },
  configureWebpack: {
    resolve: {
      fallback: {
        https: false,
        canvas: false,
        http: false,
        url: false,
        fs: false,
        crypto: false,
      },
      alias: {
        'env-router': getRouterPath(dist),
        'env-element-theme': getThemePath(dist),
        'env-pdf-viewer': getPdfViewer(pdfViewer),
      },
    },
    cache: {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename],
      },
      version: `${process.env.VUE_APP_DIST}`,
    },
    plugins: webpackPlugins,
    module: {
      rules: [
        {
          test: /svg\.font\.config.js/,
          use: [
            'vue-style-loader',
            {
              loader: 'css-loader',
              options: {
                url: false,
              },
            },
            {
              loader: 'webfonts-loader',
              options: {
                publicPath: './',
              },
            },
          ],
        },
      ],
    },
    optimization: {
      splitChunks: {
        cacheGroups: {
          handsontable: {
            test: /[\\/]node_modules[\\/]handsontable[\\/]/,
            name: 'handsontable',
            chunks: 'all',
          },
          katex: {
            test: /[\\/]node_modules[\\/]katex[\\/]/,
            name: 'katex',
            chunks: 'all',
          },
          echarts: {
            test: /[\\/]node_modules[\\/]echarts[\\/]/,
            name: 'echarts',
            chunks: 'all',
          },
          imageViewer: {
            test: /[\\/]node_modules[\\/]@paoding-label[\\/]vue-image-viewer[\\/]/,
            name: 'imageViewer',
            chunks: 'all',
          },
          pdfViewer: {
            test: /[\\/]node_modules[\\/]pdf-document-viewer[\\/]/,
            name: 'pdfViewer',
            chunks: 'all',
          },
          pdfjs: {
            test: /[\\/]node_modules[\\/](@document-kits|vue-pdf-viewer-next)[\\/]/,
            name: 'pdfjs',
            chunks: 'all',
          },
        },
      },
    },
  },
  chainWebpack: (config) => {
    if (pdfViewer === 'PDF.js') {
      config.module
        .rule('cur')
        .test(/\.(cur)(\?.*)?$/)
        .use('url-loader')
        .loader('url-loader')
        .options({
          limit: 10000,
          name: 'img/[name].[hash:8].[ext]',
        });
    }

    config
      .plugin('copy-webpack-plugin')
      .use(CopyWebpackPlugin, [copyPluginList]);
    config.plugin('html').tap((args) => {
      const option = args[0];
      option.git = {
        branch: gitRevisionPlugin.branch(),
        commit: gitRevisionPlugin.commithash(),
        time: gitRevisionPlugin.lastcommitdatetime(),
      };
      return args;
    });
  },
  pluginOptions: {
    webpackBundleAnalyzer: {
      openAnalyzer: false,
      analyzerMode: 'disabled',
    },
  },
};
